using System.Text;
using System.Text.RegularExpressions;
using KvFnB.Core.Abstractions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using KvFnB.Core.Domain.Services;

namespace KvFnB.Shared.Persistence.ShardingDb.Services
{
    /// <summary>
    /// Provides methods for generating unique sequential codes for business entities
    /// </summary>
    public class CodeGenerationService : ICodeGenerationFromTbService
    {
        private readonly ShardingDbContext _dbContext;
        private readonly ILogger<CodeGenerationService> _logger;
        private readonly ITenantProvider _tenantProvider;

        /// <summary>
        /// Initializes a new instance of the CodeGenerationService
        /// </summary>
        /// <param name="dbContext">The database context for data access</param>
        /// <param name="logger">The logger for logging operations</param>
        /// <param name="tenantProvider">The tenant provider for multi-tenancy support</param>
        public CodeGenerationService(
            ShardingDbContext dbContext,
            ILogger<CodeGenerationService> logger,
            ITenantProvider tenantProvider)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
        }

        /// <summary>
        /// Generates the next sequential code using the specified prefix and padding
        /// </summary>
        /// <param name="prefix">The prefix to use for the code (e.g., "INV-")</param>
        /// <param name="previousCode">The previous code in the sequence (optional)</param>
        /// <param name="padding">The number of digits to pad with leading zeros</param>
        /// <returns>The next sequential code</returns>
        public string GenerateNextCode(string prefix, string? previousCode, int padding)
        {
            if (string.IsNullOrEmpty(prefix))
            {
                throw new ArgumentException("Prefix cannot be null or empty", nameof(prefix));
            }

            if (padding < 1)
            {
                throw new ArgumentException("Padding must be at least 1", nameof(padding));
            }

            try
            {
                long nextNumber = 1;
                
                if (!string.IsNullOrEmpty(previousCode))
                {
                    // Extract numeric portion using regex
                    Match match = Regex.Match(previousCode, "[0-9]+", RegexOptions.None);
                    if (match.Success)
                    {
                        long currentNumber = long.Parse(match.Groups[0].Value);
                        nextNumber = currentNumber + 1;
                    }
                    else
                    {
                        _logger.LogWarning("Could not extract numeric portion from code: {PreviousCode}", previousCode);
                    }
                }

                // Format the result with prefix and zero-padded number
                return string.Format("{0}{1}", prefix, nextNumber.ToString().PadLeft(padding, '0'));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating next code with prefix {Prefix}", prefix);
                throw new InvalidOperationException("Could not determine the next code in sequence", ex);
            }
        }

        /// <summary>
        /// Asynchronously generates the next sequential code for an entity type
        /// </summary>
        /// <param name="entityType">The type of entity for which to generate the code</param>
        /// <param name="prefix">The prefix to use for the code</param>
        /// <param name="padding">The number of digits to pad with leading zeros</param>
        /// <returns>The next sequential code</returns>
        public async Task<string> GenerateNextCodeAsync(Type entityType, string prefix, int padding)
        {
            if (entityType == null)
            {
                throw new ArgumentNullException(nameof(entityType));
            }

            string tableName = _dbContext.Model.FindEntityType(entityType)?.GetTableName() ?? 
                throw new InvalidOperationException($"Could not find table name for entity type {entityType.Name}");
            
            int tenantId = _tenantProvider.GetTenantId() ?? 0;

            // Find the highest existing code using SQL query
            string? highestCode = await FindHighestCodeAsync(tableName, prefix, tenantId, padding);

            // Generate the next code in sequence
            return GenerateNextCode(prefix, highestCode, padding);
        }

        /// <summary>
        /// Asynchronously finds the highest existing code for an entity type
        /// </summary>
        /// <param name="tableName">The name of the table to query</param>
        /// <param name="prefix">The prefix to search for</param>
        /// <param name="tenantId">The tenant ID to filter by</param>
        /// <param name="padding">The number of digits to pad with leading zeros</param>
        /// <returns>The highest existing code, or null if none exists</returns>
        private async Task<string?> FindHighestCodeAsync(string tableName, string prefix, int tenantId, int padding)
        {
            // Create a SQL query to find the highest code
            string sql = @$"
                SELECT TOP 1 Code as Value
                FROM {tableName}
                WHERE RetailerId = {tenantId}
                AND Code LIKE '{prefix}%'
                AND LEN(Code) = {prefix.Length + padding}
                AND CHARINDEX('.', Code) = 0
                AND ISNUMERIC(LTRIM(RTRIM(SUBSTRING(Code, {prefix.Length + 1}, LEN(Code) - {prefix.Length})))) = 1
                ORDER BY Code DESC";

            // Execute the query and get the highest code
            string? highestCode = await _dbContext.Database
                .SqlQueryRaw<string>(sql)
                .FirstOrDefaultAsync();

            return highestCode;
        }

        /// <summary>
        /// Asynchronously checks if a code is unique and generates an alternative if it's not
        /// </summary>
        /// <param name="entityType">The type of entity to check against</param>
        /// <param name="suggestedCode">The code to check for uniqueness</param>
        /// <param name="prefix">The prefix to use if a new code needs to be generated</param>
        /// <returns>The original code if unique, or a new unique code otherwise</returns>
        public async Task<string> EnsureUniqueCodeAsync(Type entityType, string suggestedCode, string prefix)
        {
            if (string.IsNullOrWhiteSpace(suggestedCode))
            {
                throw new ArgumentException("Suggested code cannot be null or empty", nameof(suggestedCode));
            }

            string tableName = _dbContext.Model.FindEntityType(entityType)?.GetTableName() ?? 
                throw new InvalidOperationException($"Could not find table name for entity type {entityType.Name}");
            
            int tenantId = _tenantProvider.GetTenantId() ?? 0;

            // Check if the code already exists
            bool codeExists = await CheckCodeExistsAsync(tableName, suggestedCode, tenantId);

            // If the code exists, generate a random alternative
            if (codeExists)
            {
                string randomCode = $"{prefix}-{GenerateRandomCode()}";
                _logger.LogInformation("Code {SuggestedCode} already exists, generated alternative: {RandomCode}", 
                    suggestedCode, randomCode);
                
                return randomCode;
            }

            return suggestedCode;
        }

        /// <summary>
        /// Asynchronously checks if a code already exists in the database
        /// </summary>
        /// <param name="tableName">The name of the table to query</param>
        /// <param name="code">The code to check</param>
        /// <param name="tenantId">The tenant ID to filter by</param>
        /// <returns>True if the code exists, false otherwise</returns>
        private async Task<bool> CheckCodeExistsAsync(string tableName, string code, int tenantId)
        {
            // Create a SQL query to check if the code exists
            string sql = @$"
                SELECT CASE WHEN EXISTS (
                    SELECT 1
                    FROM {tableName}
                    WHERE TenantId = {tenantId}
                    AND LOWER(Code) = LOWER('{code}')
                ) THEN 1 ELSE 0 END";

            // Execute the query and check the result
            bool exists = await _dbContext.Database
                .SqlQueryRaw<bool>(sql)
                .FirstOrDefaultAsync();

            return exists;
        }

        /// <summary>
        /// Generates a random alphanumeric code
        /// </summary>
        /// <param name="length">The length of the random code to generate</param>
        /// <returns>A random uppercase alphanumeric code</returns>
        private string GenerateRandomCode(int length = 10)
        {
            // Use cryptographically secure random number generator
            Random random = new Random();
            var builder = new StringBuilder(length);
            
            for (int i = 0; i < length; i++)
            {
                // Generate random uppercase letters (A-Z)
                builder.Append((char)random.Next(0x41, 0x5A));
            }
            
            return builder.ToString();
        }
    }
} 