using KvFnB.Core.Contracts;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.CreateCustomer;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetCustomerDetail;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetListCustomer;
using KvFnB.Modules.Customer.Restful.Controllers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Xunit;

namespace KvFnB.Modules.Customer.Tests.Presentation
{
    public class CustomerControllerTests
    {
        private readonly Mock<IHttpContextAccessor> _httpContextAccessorMock;
        private readonly Mock<GetCustomerDetailUseCase> _getCustomerDetailUseCaseMock;
        private readonly Mock<GetListCustomerUseCase> _getListCustomerUseCaseMock;
        private readonly Mock<CreateCustomerUseCase> _createCustomerUseCaseMock;
        private readonly CustomerController _controller;

        public CustomerControllerTests()
        {
            _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
            _getCustomerDetailUseCaseMock = new Mock<GetCustomerDetailUseCase>();
            _getListCustomerUseCaseMock = new Mock<GetListCustomerUseCase>();
            _createCustomerUseCaseMock = new Mock<CreateCustomerUseCase>();
            
            _controller = new CustomerController(
                _httpContextAccessorMock.Object,
                _getCustomerDetailUseCaseMock.Object,
                _getListCustomerUseCaseMock.Object,
                _createCustomerUseCaseMock.Object);
        }

        [Fact]
        public async Task GetCustomerDetail_WhenSuccessful_ShouldReturnOk()
        {
            // Arrange
            var customerId = 1L;
            var response = new GetCustomerDetailResponse { Id = customerId };
            _getCustomerDetailUseCaseMock
                .Setup(x => x.ExecuteAsync(It.IsAny<GetCustomerDetailRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result<GetCustomerDetailResponse>.Success(response));

            // Act
            var result = await _controller.GetCustomerDetail(customerId, CancellationToken.None);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var successResponse = Assert.IsType<SuccessResponse<GetCustomerDetailResponse>>(okResult.Value);
            Assert.Equal(response, successResponse.Data);
        }

        [Fact]
        public async Task GetCustomerDetail_WhenFailure_ShouldReturnBadRequest()
        {
            // Arrange
            var customerId = 1L;
            var errorMessage = "Customer not found";
            _getCustomerDetailUseCaseMock
                .Setup(x => x.ExecuteAsync(It.IsAny<GetCustomerDetailRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result<GetCustomerDetailResponse>.Failure(errorMessage));

            // Act
            var result = await _controller.GetCustomerDetail(customerId, CancellationToken.None);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var errorResponse = Assert.IsType<ErrorResponse>(badRequestResult.Value);
            Assert.Equal(errorMessage, errorResponse.Message);
        }

        [Fact]
        public async Task CreateCustomer_WhenSuccessful_ShouldReturnCreatedAtAction()
        {
            // Arrange
            var request = new CreateCustomerRequest
            {
                Code = "CUST123",
                Name = "Test Customer"
            };
            var response = new CreateCustomerResponse
            {
                Id = 1,
                Code = "CUST123",
                Name = "Test Customer"
            };
            
            _createCustomerUseCaseMock
                .Setup(x => x.ExecuteAsync(request, It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result<CreateCustomerResponse>.Success(response));

            // Act
            var result = await _controller.CreateCustomer(request, CancellationToken.None);

            // Assert
            var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal(nameof(CustomerController.GetCustomerDetail), createdAtActionResult.ActionName);
            Assert.Equal(response.Id, createdAtActionResult.RouteValues!["id"]);
            Assert.Equal(response, createdAtActionResult.Value);
        }

        [Fact]
        public async Task CreateCustomer_WhenFailure_ShouldReturnBadRequest()
        {
            // Arrange
            var request = new CreateCustomerRequest();
            var errorMessages = new List<string> { "Name is required", "Code is required" };
            
            _createCustomerUseCaseMock
                .Setup(x => x.ExecuteAsync(request, It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result<CreateCustomerResponse>.Failure(errorMessages));

            // Act
            var result = await _controller.CreateCustomer(request, CancellationToken.None);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var errorResponse = Assert.IsType<ErrorResponse>(badRequestResult.Value);
            Assert.Equal(errorMessages, errorResponse.ValidationErrors);
        }
    }
} 