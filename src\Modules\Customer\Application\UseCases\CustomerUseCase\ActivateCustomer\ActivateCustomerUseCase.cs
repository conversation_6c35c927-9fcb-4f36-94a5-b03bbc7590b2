using AutoMapper;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Constants;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.Customer.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.ActivateCustomer
{
    /// <summary>
    /// Implements the ActivateCustomer use case for activating a customer
    /// </summary>
    public class ActivateCustomerUseCase : UseCaseBase<ActivateCustomerRequest, ActivateCustomerResponse>
    {
        private readonly ICustomerRepository _repository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IValidator<ActivateCustomerRequest> _validator;
        private readonly ILogger<ActivateCustomerUseCase> _logger;

        public ActivateCustomerUseCase(
            IValidator<ActivateCustomerRequest> validator,
            ICustomerRepository repository,
            IUnitOfWork unitOfWork,
            ILogger<ActivateCustomerUseCase> logger)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public override async Task<Result<ActivateCustomerResponse>> ExecuteAsync(
            ActivateCustomerRequest request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                // Validate request
                var validationResult = _validator.Validate(request);
                if (!validationResult.IsValid)
                {
                    return Result<ActivateCustomerResponse>.Failure(validationResult.Errors);
                }

                // Check if the customer exists
                var customer = await _repository.GetAsync(request.CustomerId, cancellationToken);
                if (customer == null)
                {
                    return Result<ActivateCustomerResponse>.Failure($"Customer with ID {request.CustomerId} not found");
                }

                // Activate the customer
                customer.Activate(true);
                
                // Update the customer in the database
                await _repository.UpdateAsync(customer, cancellationToken);
                
                // Commit changes
                await _unitOfWork.CommitAsync(cancellationToken);

                // Create response
                var response = new ActivateCustomerResponse
                {
                    CustomerId = customer.Id,
                    IsActivated = customer.IsActive
                };

                return Result<ActivateCustomerResponse>.Success(response);
            }
            catch (Exception ex)
            {
                // Log the full exception details
                _logger.LogError(ex, "Error activating customer with ID {CustomerId}", request.CustomerId);
                
                // Return standardized error message
                return Result<ActivateCustomerResponse>.Failure(ErrorMessages.InternalServerError);
            }
        }
    }
} 