using KvFnB.Modules.Customer.Domain.Models;
using KvFnB.Shared.Persistence.ShardingDb.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KvFnB.Shared.Persistence.ShardingDb.EntityTypeConfigurations;

public class CustomerGroupDetailEntityTypeConfiguration : BaseEntityTypeConfiguration<CustomerGroupDetail>
{
    public override void Configure(EntityTypeBuilder<CustomerGroupDetail> builder)
    {
        base.Configure(builder);
        builder.ToTable("CustomerGroupDetail");
        builder.<PERSON><PERSON><PERSON>(e => e.Id);

        builder.HasOne(e => e.Customer)
            .WithMany(e => e.CustomerGroupDetails)
            .HasForeignKey(e => e.CustomerId);

        builder.Property(e => e.CreatedDate)
            .HasColumnName("CreatedDate")
            .IsRequired()
            .HasColumnType(SqlServerColumnTypes.DATETIME);

        builder.Ignore(e => e.TenantId);

    }
}   