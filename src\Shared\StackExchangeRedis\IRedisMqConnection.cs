using StackExchange.Redis;

namespace KvFnB.Shared.StackExchangeRedis
{
    /// <summary>
    /// Interface for Redis message queue connection. Inherits IConnectionMultiplexer but
    /// adds a method to access the full implementation for specialized operations.
    /// </summary>
    /// <remarks>
    /// This interface only requires the implementation of a few key methods from IConnectionMultiplexer
    /// but provides access to the full underlying IConnectionMultiplexer implementation when needed
    /// through the AsIConnectionMultiplexer() method.
    /// </remarks>
    public interface IRedisMqConnection : IDisposable
    {
        /// <summary>
        /// Gets a value indicating whether the connection is active and connected.
        /// </summary>
        bool IsConnected { get; }
        
        /// <summary>
        /// Gets a database connection to the Redis server.
        /// </summary>
        /// <param name="db">The database to connect to.</param>
        /// <param name="asyncState">The async state to pass to the database.</param>
        /// <returns>A connection to the database.</returns>
        IDatabase GetDatabase(int db = -1, object asyncState = null);
        
        /// <summary>
        /// Gets a subscription channel to the Redis server.
        /// </summary>
        /// <param name="asyncState">The async state to pass to the subscriber.</param>
        /// <returns>A subscription channel to the server.</returns>
        ISubscriber GetSubscriber(object asyncState = null);
        
        /// <summary>
        /// Gets the underlying IConnectionMultiplexer implementation.
        /// </summary>
        /// <returns>The underlying IConnectionMultiplexer instance.</returns>
        IConnectionMultiplexer AsIConnectionMultiplexer();
    }
} 