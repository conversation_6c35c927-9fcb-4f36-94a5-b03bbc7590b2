using KvFnB.Core.Abstractions;
using KvFnB.Modules.Menu.Domain.Events;
using Microsoft.Extensions.Caching.Distributed;

namespace KvFnB.Modules.Menu.Infrastructure.DomainEvents
{
    /// <summary>
    /// Handles the ProductDeletedEvent and invalidates related caches
    /// </summary>
    public class ProductDeletedEventHandler : IDomainEventHandler<ProductDeletedEvent>
    {
        private readonly IDistributedCache _cache;
        private readonly ILogger _logger;

        public ProductDeletedEventHandler(
            IDistributedCache cache,
            ILogger _logger)
        {
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
            this._logger = _logger ?? throw new ArgumentNullException(nameof(_logger));
        }

        /// <summary>
        /// Handles the ProductDeletedEvent by invalidating related caches
        /// </summary>
        /// <param name="domainEvent">The domain event containing the product ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        public async Task Handle(ProductDeletedEvent domainEvent, CancellationToken cancellationToken = default)
        {
            try
            {
                // Invalidate product detail cache
                await _cache.RemoveAsync($"product:{domainEvent.ProductId}", cancellationToken);
                
                // Invalidate product list caches that might contain this product
                await _cache.RemoveAsync($"products:category", cancellationToken);
                await _cache.RemoveAsync($"products:all", cancellationToken);
                
                // Invalidate any variant related caches if it's a variant product
                await _cache.RemoveAsync($"product:variants:{domainEvent.ProductId}", cancellationToken);
                
                _logger.Information($"Cache invalidated for deleted product {domainEvent.ProductId}");
            }
            catch (Exception ex)
            {
                _logger.Error($"Error invalidating cache for deleted product {domainEvent.ProductId}: {ex.Message}", ex);
                // We don't rethrow the exception as this is an event handler
                // and we don't want to fail the main operation if cache invalidation fails
            }
        }
    }
} 