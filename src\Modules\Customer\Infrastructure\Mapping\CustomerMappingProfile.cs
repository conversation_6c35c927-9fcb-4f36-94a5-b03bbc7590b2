using AutoMapper;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.CreateCustomer;
using KvFnB.Modules.Customer.Application.Dtos;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.DeactivateCustomer;

namespace KvFnB.Modules.Customer.Infrastructure.Mapping
{
    public class CustomerMappingProfile : Profile
    {
        public CustomerMappingProfile()
        {
            // Customer mappings
            CreateMap<Domain.Models.Customer, CreateCustomerResponse>()
                .ForMember(dest => dest.CreatedDate, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive));
            
            CreateMap<Domain.Models.CustomerGroup, CustomerGroupResponse>();
                
            CreateMap<CreateCustomerRequest, Domain.Models.Customer>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.TenantId, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.ModifiedAt, opt => opt.Ignore())
                .ForMember(dest => dest.ModifiedBy, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedBy, opt => opt.Ignore());

            // DeactivateCustomer mappings
            CreateMap<DeactivateCustomerRequest, Domain.Models.Customer>();
            CreateMap<Domain.Models.Customer, DeactivateCustomerResponse>()
                .ForMember(dest => dest.CustomerId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.IsDeactivated, opt => opt.MapFrom(src => !src.IsActive));
        }
    }
}