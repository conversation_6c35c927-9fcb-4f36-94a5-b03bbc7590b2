using KvFnB.Core.Abstractions;

namespace KvFnB.Shared.AuditTrailMessagePublisher
{
    public class AuditTrailService : IAuditTrailService
    {
        private readonly IMessageQueueProvider _messageQueueProvider;
        private readonly ILogger _logger;
        private const string AuditQueueName = "AuditTrailLog";

        public AuditTrailService(IMessageQueueProvider messageQueueProvider, ILogger logger)
        {
            ArgumentNullException.ThrowIfNull(messageQueueProvider);
            ArgumentNullException.ThrowIfNull(logger);

            _messageQueueProvider = messageQueueProvider;
            _logger = logger;
        }

        public async Task<bool> AddLog(AuditTrailLog source)
        {
            ValidateSource(source);
            return await AddLogAsync(source);
        }

        private static void ValidateSource(AuditTrailLog source)
        {
            ArgumentNullException.ThrowIfNull(source);
        }

        private async Task<bool> AddLogAsync(AuditTrailLog source)
        {
            try
            {
                await _messageQueueProvider.PublishAsync(AuditQueueName, source);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error publishing audit trail message with LotId: {source.LotId}", ex);
                throw;
            }
        }
    }
}