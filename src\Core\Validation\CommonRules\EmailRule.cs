using System.Text.RegularExpressions;

namespace KvFnB.Core.Validation.CommonRules
{
    public static class EmailRule
    {
        private static readonly Regex EmailRegex = new Regex(
        @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
        RegexOptions.Compiled | RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrEmpty(email))
                return true;

            return EmailRegex.IsMatch(email);
        }
    }
}