using KvFnB.Core.Abstractions;
using Microsoft.Extensions.DependencyInjection;

namespace KvFnB.Shared.UseCaseFactory
{
    public class UseCaseFactory: IUseCaseFactory
    {
        private readonly IServiceProvider _serviceProvider;

        public UseCaseFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public TUseCase GetUseCase<TUseCase>()
        {
            try
            {
#pragma warning disable CS8714 // The type cannot be used as type parameter in the generic type or method. Nullability of type argument doesn't match 'notnull' constraint.
                var useCase = _serviceProvider.GetRequiredService<TUseCase>();
#pragma warning restore CS8714 // The type cannot be used as type parameter in the generic type or method. Nullability of type argument doesn't match 'notnull' constraint.
                if (useCase == null)
                {
                    throw new InvalidOperationException($"Failed to resolve use case for type '{typeof(TUseCase).Name}'");
                }

                return useCase;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to resolve use case type '{typeof(TUseCase).Name}'", ex);
            }
        }
    }
}