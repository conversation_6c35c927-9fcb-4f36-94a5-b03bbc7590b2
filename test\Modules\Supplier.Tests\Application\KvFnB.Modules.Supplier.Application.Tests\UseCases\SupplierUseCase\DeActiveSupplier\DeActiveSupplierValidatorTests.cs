﻿using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeActiveSupplier;
using Xunit;

namespace KvFnB.Modules.Supplier.Application.Tests.UseCases.SupplierUseCase.DeActiveSupplier
{
    public class DeActiveSupplierValidatorTests
    {
        private readonly DeActiveSupplierValidator _validator;

        public DeActiveSupplierValidatorTests()
        {
            _validator = new DeActiveSupplierValidator();
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        public void Validate_ShouldReturnError_WhenSupplierIdIsInvalid(int supplierId)
        {
            // Arrange
            var request = new DeActiveSupplierRequest { SupplierId = supplierId };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("Supplier ID must be greater than zero.", result.Errors);
        }

        [Fact]
        public void Validate_ShouldPass_WhenSupplierIdIsValid()
        {
            // Arrange
            var request = new DeActiveSupplierRequest { SupplierId = 123 };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }
    }
}
