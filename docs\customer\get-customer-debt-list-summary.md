# Customer Debt Management System Documentation

## Overview
The Customer Debt Management System is a component of the KiotVietFnB application that allows businesses to track, manage, and retrieve information about customer debts. This system provides API endpoints for retrieving detailed debt information with filtering and pagination capabilities.

## System Architecture
The system follows a clean architecture approach with distinct layers:
- API Layer (Controllers)
- Application Layer (Queries/Commands)
- Data Access Layer (Dapper ORM)

## Core Functionality

### API Endpoints

#### GET /v2/customers/debts
Retrieves a list of debt records for a specific customer.

**Required Permissions:** `Customer._Read`

**Query Parameters:**
- `CustomerId` (required): ID of the customer
- `Skip` (optional): Number of records to skip (default: 0)
- `Take` (optional): Number of records to retrieve (default: 10)
- `StartDate` (optional): Filter by transaction start date
- `EndDate` (optional): Filter by transaction end date

**Response:** Returns a paginated list of customer debt records with total debt amount.

### Data Model

#### CustomerDebtModel
- `DocumentCode`: Unique identifier for the debt document
- `DocumentType`: Type of document (Invoice, Payment, etc.)
- `DocumentId`: ID of the related document
- `TransDate`: Date of the transaction
- `Balance`: Current debt balance
- `Value`: Transaction value

## Implementation Details

The system processes debt information through the following flow:
1. API request is received by `CustomerApi`
2. Request is validated and permissions are checked
3. `GetListCustomerDebtQuery` is dispatched to the query handler
4. The handler executes SQL queries using Dapper to retrieve debt records
5. Results are transformed into the response model
6. Balance values are rounded according to currency settings
7. Paginated results are returned to the client

## Security Features
- Permission-based access control
- Tenant isolation through sharded database connections
- Audit trail logging

## Dependencies
- Dapper for data access
- ServiceStack for API routing
- MediatR for query/command handling
- AutoMapper for object mapping
- Multi-currency support services