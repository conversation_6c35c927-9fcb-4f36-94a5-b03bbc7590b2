﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace KvFnB.Modules.Supplier.Application.Dtos
{
    public class SupplierDto
    {
        public long Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public string Name { get; set; } = string.Empty;
        public decimal Debt { get; set; }
        public bool IsActive { get; set; }
        public decimal TotalPayment { get; set; }
        public decimal TotalPaymentWithoutReturn { get; set; }
        public string Email { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string LocationName { get; set; } = string.Empty;
        public string WardName { get; set; } = string.Empty;
        public string Company { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;
        public string TaxCode { get; set; } = string.Empty;
        public int PurchaseOrderCount { get; set; }
        public long? AddressId { get; set; }
    }
}
