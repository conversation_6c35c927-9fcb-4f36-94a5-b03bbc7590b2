using System.Reflection;
using Microsoft.AspNetCore.Mvc.Controllers;

namespace KvFnB.CoreAPI.Filters
{
    public class CoreApiControllerProvider : ControllerFeatureProvider
    {
        protected override bool IsController(TypeInfo typeInfo)
        {
            var includedControllers = new[] { 
                "CategoryController", 
                "ProductController",
                "CustomerController",
                "SupplierController",
                "PriceBookController",
                "NoteTemplateController",
                "GroupNoteTemplateController",
                "BuildInforController",
                "PingController",
                "EWalletController",
                "BankAccountController",
                "DataDeletionController",
                "EInvoiceController"
            };
            if (includedControllers.Contains(typeInfo.Name))
            {
                return true;
            }
            return false;
        }
    }
}