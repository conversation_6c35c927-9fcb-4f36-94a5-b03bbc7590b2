using KvFnB.Core.Domain;

namespace KvFnB.Modules.Customer.Domain.Models
{
    /// <summary>
    /// Represents a customer group
    /// </summary>
    public class CustomerGroup : Entity<int>, ISoftDeletableEntity, IAuditableEntity
    {
        /// <summary>
        /// The name of the customer group
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// Optional description of the customer group
        /// </summary>
        public string? Description { get; set; }
        
        /// <summary>
        /// When the entity was created
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// User ID who created the entity
        /// </summary>
        public long CreatedBy { get; set; }
        
        /// <summary>
        /// When the entity was last modified
        /// </summary>
        public DateTime? ModifiedAt { get; set; }
        
        /// <summary>
        /// User ID who last modified the entity
        /// </summary>
        public long? ModifiedBy { get; set; }
        
        /// <summary>
        /// Whether the entity is deleted (for soft delete)
        /// </summary>
        public bool? IsDeleted { get; set; }
        
        /// <summary>
        /// When the entity was deleted
        /// </summary>
        public DateTime? DeletedAt { get; set; }
        
        /// <summary>
        /// User ID who deleted the entity
        /// </summary>
        public long? DeletedBy { get; set; }

    }
} 