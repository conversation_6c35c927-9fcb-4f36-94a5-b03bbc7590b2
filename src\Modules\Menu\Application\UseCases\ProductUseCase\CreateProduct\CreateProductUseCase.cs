using KvFnB.Core.Abstractions;
using KvFnB.Core.Authentication;
using KvFnB.Core.Contracts;
using KvFnB.Core.Domain.Services;
using KvFnB.Core.Validation;
using KvFnB.Localization;
using KvFnB.Modules.Menu.Application.Contracts;
using KvFnB.Modules.Menu.Application.Dtos.Response;
using KvFnB.Modules.Menu.Domain.Entities;
using KvFnB.Modules.Menu.Domain.Enums;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Modules.Menu.Domain.Services;
using KvFnB.Modules.Menu.Domain.ValueObjects;
using KvFnB.Shared.Localization;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.CreateProduct
{
    public class CreateProductUseCase(
        ILocalizationProvider localizationProvider,
        IValidator<CreateProductRequest> validator,
        IProductRepository productRepository,
        ICategoryRepository categoryRepository,
        ITaxRepository taxRepository,
        IPriceBookRepository priceBookRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ICodeGenerationService codeGenerationService,
        ITenantProvider tenantProvider,
        IAuthUser authUser, 
        ProductPriceBookDomainService productPriceBookDomainService) : UseCaseBase<CreateProductRequest, CreateProductResponse>
    {
        private readonly ILocalizationProvider _multiLang = localizationProvider ?? throw new ArgumentNullException(nameof(localizationProvider)); // NOSONAR
        private readonly IValidator<CreateProductRequest> _validator = validator ?? throw new ArgumentNullException(nameof(validator)); // NOSONAR
        private readonly IProductRepository _productRepository = productRepository ?? throw new ArgumentNullException(nameof(productRepository)); // NOSONAR
        private readonly ICategoryRepository _categoryRepository = categoryRepository ?? throw new ArgumentNullException(nameof(categoryRepository)); // NOSONAR
        private readonly ITaxRepository _taxRepository = taxRepository ?? throw new ArgumentNullException(nameof(taxRepository)); // NOSONAR
        private readonly IPriceBookRepository _priceBookRepository = priceBookRepository ?? throw new ArgumentNullException(nameof(priceBookRepository)); // NOSONAR
        private readonly IUnitOfWork _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork)); // NOSONAR
        private readonly IMapper _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper)); // NOSONAR
        private readonly ICodeGenerationService _codeGenerationService = codeGenerationService ?? throw new ArgumentNullException(nameof(codeGenerationService)); // NOSONAR
        private readonly ITenantProvider _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider)); // NOSONAR
        private readonly IAuthUser _authUser = authUser ?? throw new ArgumentNullException(nameof(authUser)); // NOSONAR
        private readonly ProductPriceBookDomainService _productPriceBookDomainService = productPriceBookDomainService ?? throw new ArgumentNullException(nameof(productPriceBookDomainService)); // NOSONAR
        private const string PRODUCT_CODE_PREFIX = "SP";


        public override async Task<Result<CreateProductResponse>> ExecuteAsync(CreateProductRequest request, CancellationToken cancellationToken = default)
        {
            var validationResult = await ValidateRequestAsync(request, cancellationToken);
            if (!validationResult.IsSuccess)
            {
                return Result<CreateProductResponse>.Failure(validationResult.ValidationErrors ?? new List<string> { validationResult.ErrorMessage ?? "Validation failed" });
            }

            var category = await _categoryRepository.GetAsync(request.CategoryId, cancellationToken);
            if (category == null)
            {
                return Result<CreateProductResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_category_not_found));
            }

            // Create and configure product
            var product = await CreateAndConfigureProductAsync(request, cancellationToken);
            if (!product.IsSuccess || product.Value == null)
            {
                return Result<CreateProductResponse>.Failure(product.ValidationErrors ?? new List<string> { product.ErrorMessage ?? "Cập nhật sản phẩm thất bại" });
            }

            // Customizable combo
            if (request.ProductTypeId == ProductTypes.CustomizableCombo.Id)
            {
                if (request.CustomizableComboGroups == null || request.CustomizableComboGroups.Count == 0)
                {
                    return Result<CreateProductResponse>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_combo_groups_required));
                }
                
                var comboValidationResult = await ValidateCustomizableComboProductsAsync(request.CustomizableComboGroups, cancellationToken);
                if (!comboValidationResult.IsSuccess)
                {
                    return Result<CreateProductResponse>.Failure(comboValidationResult.ValidationErrors ?? [comboValidationResult.ErrorMessage ?? _multiLang.GetMessage(LocalizationKeys.man_product_msg_create_combo_groups_required)]);
                }
                
                AddCustomizableCombo(request, product.Value);
            }

            // Configure product sale branches
            ConfigureProductSaleBranches(product.Value, request);

            var result = await SaveProductAsync(product.Value, category, request, cancellationToken);
            return result;
        }

        private void ConfigureProductSaleBranches(Product product, CreateProductRequest request)
        {
            // Skip if explicitly provided not-allow sale branches exist
            if (request.NotAllowSaleBranch != null && request.NotAllowSaleBranch.Count != 0)
            {
                foreach (var notAllowedBranch in request.NotAllowSaleBranch)
                {
                    product.AddProductSaleBranch(
                        ProductSaleBranch.Create(
                            _tenantProvider.GetTenantId() ?? 0,
                            (int)notAllowedBranch,
                            product.Id,
                            false
                        )
                    );
                }
            }

            // If user is not admin, add branches without create permission
            if (!_authUser.IsAdmin && request.AllBranchIds != null && request.BranchesWithCreatePermission != null)
            {
                var branchesWithoutPermission = request.AllBranchIds
                    .Where(branchId => !request.BranchesWithCreatePermission.Contains(branchId))
                    .ToList();

                foreach (var branchId in branchesWithoutPermission)
                {
                    product.AddProductSaleBranch(
                        ProductSaleBranch.Create(
                            _tenantProvider.GetTenantId() ?? 0,
                            branchId,
                            product.Id,
                            false
                        )
                    );
                }
            }
        }

        private async Task<Result<bool>> ValidateCustomizableComboProductsAsync(List<CustomizableComboGroupDto>? comboGroups, CancellationToken cancellationToken)
        {
            if (comboGroups == null || comboGroups.Count == 0)
            {
                return Result<bool>.Success(true);
            }
            
            // Extract all product IDs from all groups and items
            var productIds = comboGroups
                .SelectMany(g => g.Items.Select(i => i.ProductId))
                .Distinct()
                .ToList();
                
            if (productIds.Count == 0)
            {
                return Result<bool>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_missing_product_ids));
            }
            
            // Get all products in one database call
            var products = await _productRepository.GetProductsByIdsAsync(productIds, cancellationToken);
            
            // Check if any product is missing
            var foundProductIds = products.Select(p => p.Id).ToHashSet();
            var missingProductIds = productIds.Where(id => !foundProductIds.Contains(id)).ToList();
            
            var validationErrors = new List<string>();
            
            // Add errors for missing products
            foreach (var missingId in missingProductIds)
            {
                validationErrors.Add(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_missing_product_ids, missingId));
            }
            
            // Validate the found products
            foreach (var product in products)
            {
                ValidateProduct(validationErrors, product);
            }

            return validationErrors.Count != 0
                ? Result<bool>.Failure(validationErrors) 
                : Result<bool>.Success(true);
        }

        private void ValidateProduct(List<string> validationErrors, Product product)
        {
            if (product.IsDeleted == true)
            {
                validationErrors.Add(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_product_deleted, product.Id));
            }

            if (product.ProductType == ProductTypes.Combo.Id && product.IsProcessedGoods != true)
            {
                validationErrors.Add(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_invalid_product_type, product.Id));
            }

            if (product.ProductType == ProductTypes.Service.Id && product.IsTimeServices == true)
            {
                validationErrors.Add(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_invalid_product_type, product.Id));
            }

            if (product.ProductType != ProductTypes.Combo.Id &&
                product.ProductType != ProductTypes.Normal.Id &&
                product.ProductType != ProductTypes.Service.Id)
            {
                validationErrors.Add(_multiLang.GetMessage(LocalizationKeys.man_product_msg_create_invalid_product_type, product.Id));
            }
        }

        private static void AddCustomizableCombo(CreateProductRequest request, Product product)
        {
            if (request.CustomizableComboGroups == null || request.CustomizableComboGroups.Count == 0)
            {
                return;
            }   
            
            foreach (var group in request.CustomizableComboGroups)
            {
                var customizableComboGroup = CreateComboGroup(product.Id, group);
                AddComboGroupItems(customizableComboGroup, group);
                product.AddComboGroup(customizableComboGroup);
            }
        }

        private static CustomizableComboGroup CreateComboGroup(long productId, CustomizableComboGroupDto group)
        {
            return CustomizableComboGroup.Create(
                productId,
                group.Name,
                group.Description,
                group.MaxQuantity,
                group.SortOrder
            );
        }

        private static void AddComboGroupItems(CustomizableComboGroup comboGroup, CustomizableComboGroupDto groupRequest)
        {
            if (groupRequest.Items == null || groupRequest.Items.Count == 0)
            {
                return;
            }
            
            foreach (var item in groupRequest.Items)
            {
                comboGroup.AddItem(CustomizableComboGroupItem.Create(
                    groupRequest.Id,
                    item.ProductId,
                    item.AdditionalPrice
                ));
            }
        }

        private async Task<Result<string>> ValidateRequestAsync(CreateProductRequest request, CancellationToken cancellationToken)
        {
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Result<string>.Failure(validationResult.Errors);
            }

            var isUniqueProductCode = await _productRepository.IsUniqueProductCodeAsync(request.Code, null, cancellationToken);
            if (!isUniqueProductCode)
            {
                return Result<string>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_code_exists));
            }

            if (string.IsNullOrEmpty(request.Code))
            {
                request.Code = await GenerateProductCodeAsync();
            }

            return Result<string>.Success(request.Code);
        }

        private async Task<string> GenerateProductCodeAsync()
        {
            return await _codeGenerationService.GenerateCodeAsync(
                _tenantProvider.GetTenantId() ?? 0,
                nameof(Product),
                PRODUCT_CODE_PREFIX,
                null,
                6
            );
        }

        private async Task<Result<Product>> CreateAndConfigureProductAsync(CreateProductRequest request, CancellationToken cancellationToken)
        {
            var product = Product.CreateProduct(
                request.Code,
                request.Name,
                request.CategoryId,
                (byte)(request.ProductTypeId ?? ProductTypes.Normal.Id),
                request.Unit,
                request.ConversionValue,
                request.Code
            );

            product.SetBasePrice(request.BasePrice);
            product.SetDescription(request.Description);
            product.SetAllowSale(request.AllowSale);
            // Set FullName to Name if not provided
            product.SetFullName(request.FullName ?? request.Name);
            
            await ConfigureProductSettingsAsync(product, request);
            
            var taxResult = await ConfigureProductTaxAsync(product, request, cancellationToken);
            if (!taxResult.IsSuccess)
            {
                return Result<Product>.Failure(taxResult.ErrorMessage ?? "Cập nhật thuế thất bại");
            }

            var branchResult = ConfigureProductBranch(product, request);
            if (!branchResult)
            {
                return Result<Product>.Failure("Cập nhật sản phẩm thất bại");
            }

            ConfigureProductImages(product, request);

            var toppingResult = await ConfigureToppingsAsync(product, request, cancellationToken);
            if (!toppingResult.IsSuccess)
            {
                return Result<Product>.Failure(toppingResult.ErrorMessage ?? "Cập nhật topping thất bại");
            }
            
            ConfigureProductShelves(product, request);
            
            ConfigureProductFormulas(product, request);

            return Result<Product>.Success(product);
        }

        private async Task<Result<bool>> ConfigureProductTaxAsync(Product product, CreateProductRequest request, CancellationToken cancellationToken)
        {
            if (request.TaxId == null)
            {
                return Result<bool>.Success(true);
            }

            var tax = await _taxRepository.GetAsync(request.TaxId.Value, cancellationToken);
            if (tax == null)
            {
                return Result<bool>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_tax_not_found));
            }

            product.SetProductTax(ProductTax.Create(tax.Id, tax.Name, tax.TaxRate, _tenantProvider.GetTenantId() ?? 0));
            return Result<bool>.Success(true);
        }

        private bool ConfigureProductBranch(Product product, CreateProductRequest request)
        {
            var branchId = _tenantProvider.GetBranchId();
            if (branchId == null)
            {
                return false;
            }

            var productBranch = ProductBranch.Create(
                _tenantProvider.GetTenantId() ?? 0,
                (int)branchId,
                request.Cost,
                request.IsFavourite,
                request.IsDisabledOnKitchen,
                request.Rank
            );

            productBranch.UpdateInventory(
                request.OnHand,
                request.MinQuantity,
                request.MaxQuantity
            );

            product.AddInventoryOnBranch(productBranch);
            return true;
        }

        private async Task<Result<bool>> ConfigurePriceBooksAsync(Product product, CreateProductRequest request, CancellationToken cancellationToken)
        {
            if (request.PriceBooks == null || request.PriceBooks.Count == 0)
            {
                return Result<bool>.Success(true);
            }

            // Get all requested price books
            var priceBookIds = request.PriceBooks.Select(pb => pb.PriceBookId).ToList();
            var priceBooks = await _priceBookRepository.GetByIdsAsync(priceBookIds, cancellationToken);

            // Check if all requested price books were found
            if (priceBooks.Count != priceBookIds.Count)
            {
                var missingPriceBookIds = priceBookIds.Except(priceBooks.Select(pb => pb.Id));
                return Result<bool>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_pricebooks_not_found, string.Join(", ", missingPriceBookIds)));
            }

            // Convert request price books to domain model price details
            var priceDetails = request.PriceBooks.Select(pb => new ProductPriceDetail
            {
                PriceBookId = pb.PriceBookId,
                Price = pb.Price
            }).ToList();

            // Use domain service to update price books
            var updatedPriceBooks = _productPriceBookDomainService.UpdateProductPrices(
                product,
                priceBooks,
                priceDetails,
                removeOthers: false);

            // Save changes
            await _priceBookRepository.UpdateRangeAsync(updatedPriceBooks, cancellationToken);

            return Result<bool>.Success(true);
        }

        private void ConfigureProductImages(Product product, CreateProductRequest request)
        {
            if (request.Images == null)
            {
                return;
            }

            foreach (var imageUrl in request.Images)
            {
                product.AddImage(imageUrl, _tenantProvider.GetTenantId());
            }
        }

        private static void ConfigureProductShelves(Product product, CreateProductRequest request)
        {
            if (request.ProductShelves == null)
            {
                return;
            }

            foreach (var shelf in request.ProductShelves)
            {
                product.AddShelf(shelf.ShelvesId);
            }
        }

        private void ConfigureProductFormulas(Product product, CreateProductRequest request)
        {
            if (request.ProductFormulas == null)
            {
                return;
            }

            foreach (var formula in request.ProductFormulas)
            {
                product.AddFormula(formula.MaterialId, formula.Quantity, _tenantProvider.GetTenantId() ?? 0);
            }
        }

        private async Task<Result<bool>> ConfigureToppingsAsync(Product product, CreateProductRequest request, CancellationToken cancellationToken)
        {
            if (request.ToppingProductIds == null || request.ToppingProductIds.Count == 0)
            {
                return Result<bool>.Success(true);
            }

            // Get all toppings in one call
            var toppingProducts = await _productRepository.GetProductsByIdsAsync(request.ToppingProductIds, cancellationToken);
            
            // Verify all requested toppings were found
            if (toppingProducts.Count() != request.ToppingProductIds.Count)
            {
                var foundIds = toppingProducts.Select(p => p.Id).ToHashSet();
                var missingIds = request.ToppingProductIds.Where(id => !foundIds.Contains(id));
                return Result<bool>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_topping_product_not_found, string.Join(", ", missingIds)));
            }
            
            // Verify all are topping products
            var nonToppingProducts = toppingProducts.Where(p => p.IsTopping != true).ToList();
            if (nonToppingProducts.Count != 0)
            {
                var invalidIds = nonToppingProducts.Select(p => p.Id);
                return Result<bool>.Failure(_multiLang.GetMessage(LocalizationKeys.man_product_msg_update_invalid_topping, string.Join(", ", invalidIds)));
            }
            
            // Add all validated toppings
            foreach (var toppingId in request.ToppingProductIds)
            {
                product.AddTopping(toppingId, _tenantProvider.GetTenantId());
            }

            return Result<bool>.Success(true);
        }

        private async Task<Result<CreateProductResponse>> SaveProductAsync(Product product, Category category, CreateProductRequest request, CancellationToken cancellationToken)
        {
            // First save the product to get its ID
            product = await _productRepository.AddAsync(product, cancellationToken);
            await _unitOfWork.CommitAsync(cancellationToken);

            if (product.Id > 0)
            {
                var priceBookResult = await ConfigurePriceBooksAsync(product, request, cancellationToken);
                if (!priceBookResult.IsSuccess)
                {
                    return Result<CreateProductResponse>.Failure(priceBookResult.ErrorMessage ?? _multiLang.GetMessage(LocalizationKeys.man_product_msg_update_pricebooks_not_found));
                }
            }
            
            await _unitOfWork.CommitAsync(cancellationToken);
            var response = _mapper.Map<CreateProductResponse>(product);
            response.Category = _mapper.Map<CategoryResponse>(category);
            
            // Map customizable combo groups if this is a customizable combo product
            if (product.ProductType == ProductTypes.CustomizableCombo.Id && product.ComboGroups.Count != 0)
            {
                response.CustomizableComboGroups = _mapper.Map<List<CustomizableComboGroupDto>>(product.ComboGroups);
            }
            return Result<CreateProductResponse>.Success(response);
        }

        private static Task ConfigureProductSettingsAsync(Product product, CreateProductRequest request)
        {
            if (request.ProductTypeId.HasValue)
                product.UpdateProductType((byte)request.ProductTypeId.Value);

            if (request.InventoryTrackingIgnore.HasValue)
                product.UpdateInventorySettings(request.InventoryTrackingIgnore.Value);

            if (request.Rank.HasValue)
                product.UpdateRank(request.Rank.Value);

            if (request.ProductGroupId.HasValue)
                product.UpdateProductGroup((byte)request.ProductGroupId.Value);
                
            if (request.IsRewardPoint.HasValue)
                product.UpdateIsRewardPoint(request.IsRewardPoint.Value);
                
            if (!string.IsNullOrEmpty(request.OrderTemplate))
                product.SetOrderTemplate(request.OrderTemplate);

            return Task.CompletedTask;
        }
    }
} 