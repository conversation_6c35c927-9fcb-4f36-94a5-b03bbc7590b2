using System.ComponentModel;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.ActivateCustomer
{
    /// <summary>
    /// Response model for the activate customer use case
    /// </summary>
    public class ActivateCustomerResponse
    {
        /// <summary>
        /// The ID of the customer that was activated
        /// </summary>
        [JsonPropertyName("customer_id"), Description("The unique identifier of the customer that was activated.")]
        public long CustomerId { get; set; }

        /// <summary>
        /// Indicates whether the customer was successfully activated
        /// </summary>
        [JsonPropertyName("is_activated"), Description("Indicates whether the customer was successfully activated.")]
        public bool IsActivated { get; set; }
    }
} 