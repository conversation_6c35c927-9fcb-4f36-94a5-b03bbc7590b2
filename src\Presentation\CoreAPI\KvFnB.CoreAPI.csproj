<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.6" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="7.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Modules\DataManagement\Infrastructure\KvFnB.Modules.DataManagement.Infrastructure.csproj" />
    <ProjectReference Include="..\..\Modules\DataManagement\Presentation\KvFnB.Modules.DataManagement.Restful\KvFnB.Modules.DataManagement.Restful.csproj" />
    <ProjectReference Include="..\..\Modules\Menu\Infrastructure\KvFnB.Modules.Menu.Infrastructure.csproj" />
    <ProjectReference Include="..\..\Modules\Menu\Presentation\Restful\KvFnB.Modules.Menu.Restful.csproj" />
    <ProjectReference Include="..\..\Modules\Payment\KvFnB.Modules.Payment.Infrastructure\KvFnB.Modules.Payment.Infrastructure.csproj" />
    <ProjectReference Include="..\..\Modules\Payment\Presentation\Restful\KvFnB.Modules.Payment.Restful\KvFnB.Modules.Payment.Restful.csproj" />
    <ProjectReference Include="..\..\Modules\Supplier\Infrastructure\KvFnB.Modules.Supplier.Infrastructure\KvFnB.Modules.Supplier.Infrastructure.csproj" />
    <ProjectReference Include="..\..\Modules\Supplier\Presentation\Restful\KvFnB.Modules.Supplier.Restful\KvFnB.Modules.Supplier.Restful.csproj" />
    <ProjectReference Include="..\..\Shared\KvFnB.Shared.csproj" />
    <ProjectReference Include="..\..\Modules\Customer\Infrastructure\KvFnB.Modules.Customer.Infrastructure.csproj" />
    <ProjectReference Include="..\..\Modules\Customer\Presentation\Restful\KvFnB.Modules.Customer.Restful.csproj" />
  </ItemGroup>
</Project>
