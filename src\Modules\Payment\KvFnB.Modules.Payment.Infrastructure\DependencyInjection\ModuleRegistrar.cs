﻿using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.Payment.Application.UseCases.BankAccountUseCases.CreateBankAccountUseCase;
using KvFnB.Modules.Payment.Application.UseCases.BankAccountUseCases.DeleteBankAccountUseCase;
using KvFnB.Modules.Payment.Application.UseCases.BankAccountUseCases.GetAllBankAccountUseCase;
using KvFnB.Modules.Payment.Application.UseCases.BankAccountUseCases.GetBankAccount;
using KvFnB.Modules.Payment.Application.UseCases.BankAccountUseCases.UpdateBankAccountUseCase;
using KvFnB.Modules.Payment.Application.UseCases.CheckDigitalSignatureStatus;
using KvFnB.Modules.Payment.Application.UseCases.EWalletUseCases.CreateEWalletUseCase;
using KvFnB.Modules.Payment.Application.UseCases.EWalletUseCases.DeleteEWalletUseCase;
using KvFnB.Modules.Payment.Application.UseCases.EWalletUseCases.GetAllEWalletUseCase;
using KvFnB.Modules.Payment.Application.UseCases.EWalletUseCases.UpdateEWalletUseCase;
using KvFnB.Modules.Payment.Application.UseCases.GetEInvoiceQuota;
using KvFnB.Modules.Payment.Domain.Repositories;
using KvFnB.Modules.Payment.Infrastructure.Mapping;
using KvFnB.Modules.Payment.Infrastructure.Persistence;
using KvFnB.Modules.Payment.Infrastructure.Services.Kfin;
using KvFnB.Shared.KmaService;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace KvFnB.Modules.Payment.Infrastructure.DependencyInjection
{
    public static class ModuleRegistrar
    {
        public static void AddPaymentModule(this IServiceCollection services, IConfiguration configuration)
        {
            // Register use cases
            services.AddScoped<CreateEWalletUseCase>();
            services.AddScoped<GetAllEWalletUseCase>();
            services.AddScoped<UpdateEWalletUseCase>();
            services.AddScoped<DeleteEWalletUseCase>();
            services.AddScoped<CreateBankAccountUseCase>();
            services.AddScoped<GetAllBankAccountUseCase>();
            services.AddScoped<UpdateBankAccountUseCase>();
            services.AddScoped<DeleteBankAccountUseCase>();
            services.AddScoped<GetBankAccountUseCase>();
            services.AddScoped<CheckDigitalSignatureUseCase>();
            services.AddScoped<GetEInvoiceQuotaUseCase>();

            // Register validators
            services.AddScoped<IValidator<CreateEWalletRequest>, CreateEWalletValidator>();
            services.AddScoped<IValidator<CreateBankAccountRequest>, CreateBankAccountValidator>();
            services.AddScoped<IValidator<UpdateBankAccountRequest>, UpdateBankAccountValidator>();
            services.AddScoped<IValidator<UpdateEWalletRequest>, UpdateEWalletValidator>();
            services.AddScoped<IValidator<CheckDigitalSignatureRequest>, CheckDigitalSignatureValidator>();
            services.AddScoped<IValidator<GetEInvoiceQuotaRequest>, GetEInvoiceQuotaValidator>();

            // Register repositories
            services.AddScoped<IEWalletRepository, EWalletRepository>();
            services.AddScoped<IBankAccountRepository, BankAccountRepository>();

            // Register AutoMapper profiles
            services.AddAutoMapper(typeof(PaymentMappingProfile));

            // Register Kfin
            services.AddKfinService(configuration);

            // Register KMA service
            services.AddScoped<IKmaService, KmaService>();
        }
    }
}
