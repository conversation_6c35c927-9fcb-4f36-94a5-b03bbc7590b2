# UseCase Factory Implementation

## Overview

This document describes the implementation of the UseCase Factory pattern for the Customer module, which reduces constructor parameter count and provides lazy loading of use cases.

## Problem Solved

**Before**: The CustomerController had 6 constructor parameters (3 use cases + 3 other dependencies), violating the clean code guideline of maximum 7 parameters.

**After**: The CustomerController now has 4 constructor parameters (1 factory + 3 other dependencies), following clean code principles.

## Architecture

### 1. Generic Factory Interface (`IUseCaseFactory`)

Located: `src/Core/Abstractions/IUseCaseFactory.cs`

```csharp
/// <summary>
/// Generic factory interface for creating use cases
/// </summary>
[Description("Generic factory interface for creating use cases with lazy loading support.")]
public interface IUseCaseFactory 
{
    /// <summary>
    /// Gets the appropriate use case for the specified type
    /// </summary>
    /// <typeparam name="TUseCase">The type of use case to retrieve</typeparam>
    /// <exception cref="ArgumentException">Thrown when no use case is found for the specified type</exception>
    [Description("Retrieves a use case instance by type with lazy initialization.")]
    TUseCase GetUseCase<TUseCase>();
}
```

### 2. Shared Factory Implementation

Located: `src/Shared/UseCaseFactory/UseCaseFactory.cs`

```csharp
public class UseCaseFactory: IUseCaseFactory
{
    private readonly IServiceProvider _serviceProvider;

    public UseCaseFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public TUseCase GetUseCase<TUseCase>()
    {
        try
        {
            var useCase = _serviceProvider.GetRequiredService<TUseCase>();
            if (useCase == null)
            {
                throw new InvalidOperationException($"Failed to resolve use case for type '{typeof(TUseCase).Name}'");
            }

            return useCase;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to resolve use case type '{typeof(TUseCase).Name}'", ex);
        }
    }
}
```

Key features:
- **Simple Design**: Uses generic type resolution without complex key mapping
- **Lazy Loading**: Use cases are resolved only when needed from DI container
- **Dependency Injection**: Uses IServiceProvider for direct type resolution
- **Error Handling**: Comprehensive exception handling with meaningful messages
- **Shared Implementation**: Single factory implementation across all modules

### 3. Updated Controller

Located: `src/Modules/Customer/Presentation/Restful/CustomerController.cs`

**Before**:
```csharp
public CustomerController(
    IHttpContextAccessor httpContextAccessor,
    GetCustomerDetailUseCase getCustomerDetailUseCase,
    GetListCustomerUseCase getListCustomerUseCase,
    DeleteCustomerUseCase deleteCustomerUseCase,
    IPermissionService permissionService,
    ITenantProvider tenantProvider)
```

**After**:
```csharp
public CustomerController(
    IHttpContextAccessor httpContextAccessor,
    IUseCaseFactory useCaseFactory,
    IPermissionService permissionService,
    ITenantProvider tenantProvider)
```

## Usage Examples

### Direct Type Resolution
```csharp
// In controller action
var useCase = _useCaseFactory.GetUseCase<GetCustomerDetailUseCase>();
var result = await useCase.ExecuteAsync(request, cancellationToken);
```

### Multiple Use Cases in Single Action
```csharp
// Using different use cases in the same controller
var detailUseCase = _useCaseFactory.GetUseCase<GetCustomerDetailUseCase>();
var listUseCase = _useCaseFactory.GetUseCase<GetListCustomerUseCase>();
var deleteUseCase = _useCaseFactory.GetUseCase<DeleteCustomerUseCase>();
```

## Benefits

1. **Reduced Constructor Parameters**: From 6 to 4 parameters
2. **Lazy Loading**: Use cases are created only when needed
3. **Better Testability**: Easy to mock the factory interface
4. **Scalability**: Easy to add new use cases without changing controller constructor
5. **Simplicity**: No complex key mapping or customer-specific interfaces needed
6. **Type Safety**: Compile-time checking with generic types
7. **Shared Implementation**: Single factory works across all modules

## Dependency Injection Registration

Located: `src/Modules/Customer/Infrastructure/DependencyInjection/ModuleRegistrar.cs`

```csharp
// Register individual use cases (required for factory resolution)
services.AddScoped<GetListCustomerUseCase>();
services.AddScoped<GetCustomerDetailUseCase>();
services.AddScoped<DeleteCustomerUseCase>();
services.AddScoped<CreateCustomerUseCase>();
services.AddScoped<ActivateCustomerUseCase>();
services.AddScoped<DeactivateCustomerUseCase>();

// Register the shared factory
services.AddScoped<IUseCaseFactory, UseCaseFactory>();
```

## Error Handling

The factory implementation includes:
- Service resolution error handling with meaningful exception messages
- Null validation for resolved services
- Exception wrapping with context information
- **Compliance with Error Handling Guidelines**: No internal exception details exposed to clients

## Testing Considerations

### Unit Testing the Factory
```csharp
[Test]
public void GetUseCase_ShouldReturnCorrectInstance()
{
    // Arrange
    var serviceProvider = Mock.Of<IServiceProvider>();
    var expectedUseCase = new GetCustomerDetailUseCase(/* dependencies */);
    
    Mock.Get(serviceProvider)
        .Setup(sp => sp.GetRequiredService<GetCustomerDetailUseCase>())
        .Returns(expectedUseCase);
    
    var factory = new UseCaseFactory(serviceProvider);
    
    // Act
    var result = factory.GetUseCase<GetCustomerDetailUseCase>();
    
    // Assert
    Assert.Same(expectedUseCase, result);
}
```

### Testing Controllers with Factory
```csharp
[Test]
public async Task GetCustomerDetail_ShouldReturnSuccess()
{
    // Arrange
    var factoryMock = new Mock<IUseCaseFactory>();
    var useCaseMock = new Mock<GetCustomerDetailUseCase>();
    
    factoryMock.Setup(f => f.GetUseCase<GetCustomerDetailUseCase>())
               .Returns(useCaseMock.Object);
    
    var controller = new CustomerController(
        httpContextAccessor,
        factoryMock.Object,
        permissionService,
        tenantProvider);
    
    // Act & Assert
    // ... test implementation
}
```

### Testing Exception Scenarios
```csharp
[Test]
public void GetUseCase_WhenServiceNotRegistered_ShouldThrowMeaningfulException()
{
    // Arrange
    var serviceProvider = Mock.Of<IServiceProvider>();
    Mock.Get(serviceProvider)
        .Setup(sp => sp.GetRequiredService<GetCustomerDetailUseCase>())
        .Throws(new InvalidOperationException("Service not registered"));
    
    var factory = new UseCaseFactory(serviceProvider);
    
    // Act & Assert
    var exception = Assert.Throws<InvalidOperationException>(() => 
        factory.GetUseCase<GetCustomerDetailUseCase>());
    
    Assert.Contains("Failed to resolve use case type 'GetCustomerDetailUseCase'", exception.Message);
}
```

## Implementation Details

### Why This Approach Was Chosen

1. **Simplicity**: No need for complex enum-based key mapping
2. **Type Safety**: Direct generic type resolution provides compile-time safety
3. **Reusability**: Single implementation works across all modules
4. **Maintainability**: Less code to maintain compared to module-specific factories
5. **Performance**: Direct DI resolution without additional mapping layers

### Comparison with Alternative Approaches

| Approach | Pros | Cons |
|----------|------|------|
| **Direct DI (Before)** | Simple, straightforward | Many constructor parameters |
| **Key-Based Factory** | Centralized mapping | Complex key management, runtime errors |
| **Type-Based Factory (Current)** | Simple, type-safe, reusable | Requires all use cases in DI |

## Future Enhancements

1. **Caching**: Add caching layer for frequently used use cases
2. **Metrics**: Add performance monitoring for use case resolution
3. **Async Resolution**: Support for async use case initialization
4. **Scoped Factories**: Create module-specific factory extensions if needed

## Compliance

This implementation follows:
- ✅ **Clean Code Guidelines**: Constructor parameters ≤ 7 (reduced from 6 to 4)
- ✅ **Architecture Guidelines**: Proper separation of concerns with shared implementation
- ✅ **Error Handling Guidelines**: Standardized error messages, no exception details exposed to clients
- ✅ **Documentation Guidelines**: Comprehensive XML documentation with descriptions
- ✅ **DRY Principle**: Single factory implementation shared across modules
- ✅ **SOLID Principles**: Interface segregation and dependency inversion 