﻿using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.ActiveSupplier;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using KvFnB.Core.Abstractions;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeActiveSupplier;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeleteSupplier;
using KvFnB.Core.Exceptions;

namespace KvFnB.Modules.Supplier.Restful.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class SupplierController : BaseApi
    {
        private readonly ActiveSupplierUseCase _activeSupplierUseCase;
        private readonly DeActiveSupplierUseCase _deactiveSupplierUseCase;
        private readonly DeleteSupplierUseCase _deleteSupplierUseCase;
        private readonly IPermissionService _permissionService;
        private readonly ITenantProvider _tenantProvider;


        public SupplierController(
            ActiveSupplierUseCase activeSupplierUseCase,
            DeActiveSupplierUseCase deactiveSupplierUseCase,
            DeleteSupplierUseCase deleteSupplierUseCase,
            IHttpContextAccessor httpContextAccessor,
            IPermissionService permissionService,
            ITenantProvider tenantProvider
            )
            : base(httpContextAccessor)
        {
            _activeSupplierUseCase = activeSupplierUseCase ?? throw new ArgumentNullException(nameof(activeSupplierUseCase));
            _deactiveSupplierUseCase = deactiveSupplierUseCase ?? throw new ArgumentNullException(nameof(deactiveSupplierUseCase));
            _deleteSupplierUseCase = deleteSupplierUseCase ?? throw new ArgumentNullException(nameof(deleteSupplierUseCase));
            _permissionService = permissionService ?? throw new ArgumentNullException(nameof(permissionService));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
        }

        /// <summary>
        /// Activates a supplier that was previously deactivated.
        /// </summary>
        [HttpPut("active/{supplierId}")]
        [SwaggerOperation(
            Summary = "Activate a supplier",
            Description = "Activates a supplier that was previously deactivated.",
            OperationId = "ActiveSupplier",
            Tags = new[] { "Supplier" }
        )]
        [SwaggerResponse(StatusCodes.Status200OK, "Successfully actived the supplier", typeof(ActiveSupplierResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid supplier ID or supplier not found", typeof(ErrorResponse))]
        [SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized")]
        [SwaggerResponse(StatusCodes.Status403Forbidden, "Forbidden - No permission to delete this supplier")]
        public async Task<IActionResult> ActivateSupplier([FromRoute] int supplierId, CancellationToken cancellationToken)
        {
            await CheckPermission("Supplier_Update");

            var request = new ActiveSupplierRequest { SupplierId = supplierId };
            var result = await _activeSupplierUseCase.ExecuteAsync(request, cancellationToken);

            if (!result.IsSuccess)
            {
                return BadRequest(new ErrorResponse(result.ErrorMessage ?? "Failed to active supplier", result.ValidationErrors));
            }

            return Ok(result.Value);
        }

        /// <summary>
        /// DeActivates a supplier that was previously activated.
        /// </summary>
        [HttpPut("deactive/{supplierId}")]
        [SwaggerOperation(
            Summary = "DeActivate a supplier",
            Description = "DeActivates a supplier that was previously activated.",
            OperationId = "DeactiveSupplier",
            Tags = new[] { "Supplier" }
        )]
        [SwaggerResponse(StatusCodes.Status200OK, "Successfully Deactived the supplier", typeof(DeActiveSupplierResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid supplier ID or supplier not found", typeof(ErrorResponse))]
        [SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized")]
        [SwaggerResponse(StatusCodes.Status403Forbidden, "Forbidden - No permission to deactive this supplier")]
        public async Task<IActionResult> DeActivateSupplier([FromRoute] int supplierId, CancellationToken cancellationToken)
        {
            await CheckPermission("Supplier_Update");

            var request = new DeActiveSupplierRequest { SupplierId = supplierId };
            var result = await _deactiveSupplierUseCase.ExecuteAsync(request, cancellationToken);

            if (!result.IsSuccess)
            {
                return BadRequest(new ErrorResponse(result.ErrorMessage ?? "Failed to deactive supplier", result.ValidationErrors));
            }

            return Ok(result.Value);
        }

        [HttpDelete("delete/{supplierId}")]
        [SwaggerOperation(
            Summary = "Soft delete a supplier",
            Description = "Marks a supplier as deleted and updates identifying fields.",
            OperationId = "DeleteSupplier",
            Tags = new[] { "Supplier" }
        )]
        [SwaggerResponse(StatusCodes.Status200OK, "Successfully deleted the supplier", typeof(DeleteSupplierResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid supplier ID or supplier not found", typeof(ErrorResponse))]
        [SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized")]
        [SwaggerResponse(StatusCodes.Status403Forbidden, "Forbidden - No permission to delete this supplier")]
        public async Task<IActionResult> DeleteSupplier([FromRoute] int supplierId, CancellationToken cancellationToken)
        {
            await CheckPermission("Supplier_Delete");

            var request = new DeleteSupplierRequest { SupplierId = supplierId };
            var result = await _deleteSupplierUseCase.ExecuteAsync(request, cancellationToken);

            if (!result.IsSuccess)
            {
                return BadRequest(new ErrorResponse(result.ErrorMessage ?? "Failed to delete supplier", result.ValidationErrors));
            }

            return Ok(result.Value);
        }

        private async Task CheckPermission(string permissionKey)
        {
            if (_permissionService.IsAdmin) return;
            var permissions = await _permissionService.GetUserPermissionsAsync();
            var branchIds = permissions.FirstOrDefault(p => p.Key == permissionKey).Value;

            if (branchIds == null || branchIds.Count == 0 || !branchIds.Contains(_tenantProvider.GetBranchId() ?? 0))
            {
                throw new ForbiddenException($"User does not have {permissionKey} permission.");
            }
        }
    }
}
