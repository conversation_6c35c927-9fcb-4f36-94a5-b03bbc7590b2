using System.Text.Json.Serialization;

namespace KvFnB.Core.Models;

/// <summary>
/// API response from KMA service
/// </summary>
public class KmaVerifyPhoneNumberApiResponse
{
    /// <summary>
    /// Indicates if the operation was valid/successful
    /// </summary>
    [JsonPropertyName("is_valid")]
    public bool IsValid { get; set; }

    /// <summary>
    /// Message from the API (typically empty on success, contains error message on failure)
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;
}