using AutoMapper;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Constants;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.Customer.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.DeactivateCustomer
{
    /// <summary>
    /// Implements the DeactivateCustomer use case for deactivating a customer
    /// </summary>
    public class DeactivateCustomerUseCase : UseCaseBase<DeactivateCustomerRequest, DeactivateCustomerResponse>
    {
        private readonly ICustomerRepository _repository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IValidator<DeactivateCustomerRequest> _validator;
        private readonly AutoMapper.IMapper _mapper;
        private readonly ILogger<DeactivateCustomerUseCase> _logger;

        public DeactivateCustomerUseCase(
            IValidator<DeactivateCustomerRequest> validator,
            ICustomerRepository repository,
            IUnitOfWork unitOfWork,
            AutoMapper.IMapper mapper,
            ILogger<DeactivateCustomerUseCase> logger)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public override async Task<Result<DeactivateCustomerResponse>> ExecuteAsync(
            DeactivateCustomerRequest request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                // Validate request
                var validationResult = _validator.Validate(request);
                if (!validationResult.IsValid)
                {
                    return Result<DeactivateCustomerResponse>.Failure(validationResult.Errors);
                }

                // Check if the customer exists
                var customer = await _repository.GetAsync(request.CustomerId, cancellationToken);
                if (customer == null)
                {
                    return Result<DeactivateCustomerResponse>.Failure($"Customer with ID {request.CustomerId} not found");
                }

                // Deactivate the customer
                customer.Activate(false);
                
                // Update the customer in the database
                await _repository.UpdateAsync(customer, cancellationToken);
                
                // Commit changes
                await _unitOfWork.CommitAsync(cancellationToken);

                // Create response
                var response = new DeactivateCustomerResponse
                {
                    CustomerId = customer.Id,
                    IsDeactivated = true
                };

                return Result<DeactivateCustomerResponse>.Success(response);
            }
            catch (Exception ex)
            {
                // Log the full exception details
                _logger.LogError(ex, "Error deactivating customer with ID {CustomerId}", request.CustomerId);
                
                // Return standardized error message
                return Result<DeactivateCustomerResponse>.Failure(ErrorMessages.InternalServerError);
            }
        }
    }
} 