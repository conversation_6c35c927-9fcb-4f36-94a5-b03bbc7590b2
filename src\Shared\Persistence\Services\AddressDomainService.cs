using KvFnB.Core.Abstractions;
using KvFnB.Core.Domain.Models;
using KvFnB.Core.Domain.Repositories;
using KvFnB.Core.Domain.Services;

namespace KvFnB.Shared.Persistence.Services
{
    /// <summary>
    /// Implementation of the address domain service
    /// </summary>
    public class AddressDomainService : IAddressDomainService
    {
        private readonly IAddressRepository _addressRepository;
        private readonly IUnitOfWork _unitOfWork;
        
        /// <summary>
        /// Initializes a new instance of the <see cref="AddressDomainService"/> class
        /// </summary>
        /// <param name="addressRepository">The address repository</param>
        /// <param name="unitOfWork">The unit of work</param>
        /// <exception cref="ArgumentNullException">Thrown when any dependency is null</exception>
        public AddressDomainService(
            IAddressRepository addressRepository,
            IUnitOfWork unitOfWork)
        {
            _addressRepository = addressRepository ?? throw new ArgumentNullException(nameof(addressRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        /// <inheritdoc/>
        public async Task SaveAddressLocation(Address address, CancellationToken cancellationToken = default)
        {
            // 1) Check if the address is provided
            if (address == null)
            {
                throw new ArgumentNullException(nameof(address));
            }
            
            var addressId = address.Id;
            
            // 2) Handle update scenario - existing address (Id > 0)
            if (addressId > 0)
            {
                // Retrieve the current address from the repository
                var currentAddress = await _addressRepository.GetAsync(addressId, cancellationToken);
                
                // Update the address properties with the new values
                currentAddress?.UpdateAddress(
                    address.AddressLine,
                    address.AdministrativeAreaId,
                    address.PostalCode);
                
                // Save the updated address to the repository
                if (currentAddress != null)
                {
                    await _addressRepository.UpdateAsync(currentAddress, cancellationToken);
                }
            }
            
            // 3) Handle creation scenario - new address (Id == 0 && AdministrativeAreaId > 0)
            if (address.AdministrativeAreaId > 0 && addressId == 0)
            {
                // Add the new address to the repository
                _ = await _addressRepository.AddAsync(address, cancellationToken);
                
                // Commit the changes to the database
                await _unitOfWork.CommitAsync(cancellationToken);
            }
        }
    }
} 