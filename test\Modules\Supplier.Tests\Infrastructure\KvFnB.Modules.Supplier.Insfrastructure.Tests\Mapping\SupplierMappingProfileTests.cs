﻿using AutoMapper;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.ActiveSupplier;
using KvFnB.Modules.Supplier.Infrastructure.Mapping;
using Xunit;
using IMapper = AutoMapper.IMapper;
using Assert = Xunit.Assert;
using KvFnB.Modules.Supplier.Application.Dtos;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeActiveSupplier;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeleteSupplier;

namespace KvFnB.Modules.Supplier.Insfrastructure.Tests.Mapping
{
    public class SupplierMappingProfileTests
    {
        private readonly IMapper _mapper;

        public SupplierMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg => cfg.AddProfile<SupplierMappingProfile>());
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void MappingConfiguration_ShouldBeValid()
        {
            var config = new MapperConfiguration(cfg => cfg.AddProfile<SupplierMappingProfile>());
            config.AssertConfigurationIsValid();
        }

        [Fact]
        public void Should_Map_Supplier_To_SupplierDto_Correctly()
        {
            var supplier = new Domain.Models.Supplier
            {
                Id = 1,
                Code = "NCC001",
                Name = "Test Supplier",
                Email = "<EMAIL>",
                Phone = "099999999"
            };

            var dto = _mapper.Map<SupplierDto>(supplier);

            Assert.Equal(supplier.Id, dto.Id);
            Assert.Equal(supplier.Name, dto.Name);
            Assert.Equal(supplier.Email, dto.Email);
            Assert.Equal(supplier.Code, dto.Code);
            Assert.Equal(supplier.Phone, dto.Phone);
        }

        [Fact]
        public void Should_Map_Supplier_To_ActiveSupplierResponse_Correctly()
        {
            var supplier = new Domain.Models.Supplier
            {
                Id = 2,
                Name = "Active Supplier",
                IsActive = true
            };

            var response = _mapper.Map<ActiveSupplierResponse>(supplier);

            Assert.Equal(supplier.Id, response.Id);
            Assert.Equal(supplier.Name, response.Name);
            Assert.True(response.IsActive);
        }

        [Fact]
        public void Should_Map_Supplier_To_DeActiveSupplierResponse_Correctly()
        {
            var supplier = new Domain.Models.Supplier
            {
                Id = 2,
                Name = "DeActive Supplier",
                IsActive = false
            };

            var response = _mapper.Map<DeActiveSupplierResponse>(supplier);

            Assert.Equal(supplier.Id, response.Id);
            Assert.Equal(supplier.Name, response.Name);
            Assert.False(response.IsActive);
        }

        [Fact]
        public void Should_Map_Supplier_To_DeleteSupplierResponse_Correctly()
        {
            var supplier = new Domain.Models.Supplier
            {
                Id = 1,
                Code = "NCC001",
                Name = "Test Supplier",
                Phone = "099999999",
                IsDeleted = true
            };

            var response = _mapper.Map<DeleteSupplierResponse>(supplier);

            Assert.Equal(supplier.Id, response.Id);
            Assert.Equal(supplier.Name, response.Name);
            Assert.Equal(supplier.Code, response.Code);
            Assert.Equal(supplier.Phone, response.Phone);
            Assert.True(response.IsDeleted);
        }
    }
}
