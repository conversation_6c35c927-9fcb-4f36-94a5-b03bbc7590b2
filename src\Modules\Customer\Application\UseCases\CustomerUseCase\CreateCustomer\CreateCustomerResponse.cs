using System.ComponentModel;
using System.Text.Json.Serialization;
using System.Collections.Generic;
using KvFnB.Modules.Customer.Application.Dtos;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.CreateCustomer
{
    /// <summary>
    /// Represents the response model for the CreateCustomer use case
    /// </summary>
    public class CreateCustomerResponse
    {
        /// <summary>
        /// The unique identifier of the customer
        /// </summary>
        [JsonPropertyName("id")]
        [Description("The unique identifier of the customer")]
        public long Id { get; set; }
        
        /// <summary>
        /// The unique code for the customer
        /// </summary>
        [JsonPropertyName("code")]
        [Description("The unique code for the customer")]
        public string Code { get; set; } = string.Empty;
        
        /// <summary>
        /// The name of the customer
        /// </summary>
        [JsonPropertyName("name")]
        [Description("The name of the customer")]
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// The email address of the customer
        /// </summary>
        [JsonPropertyName("email")]
        [Description("The email address of the customer")]
        public string? Email { get; set; }
        
        /// <summary>
        /// The contact number of the customer
        /// </summary>
        [JsonPropertyName("contact_number")]
        [Description("The contact number of the customer")]
        public string? ContactNumber { get; set; }
        
        /// <summary>
        /// The type of customer (1: Individual, 2: Corporate)
        /// </summary>
        [JsonPropertyName("type")]
        [Description("The type of customer (1: Individual, 2: Corporate)")]
        public byte Type { get; set; }
        
        /// <summary>
        /// Whether the customer is active
        /// </summary>
        [JsonPropertyName("is_active")]
        [Description("Whether the customer is active")]
        public bool IsActive { get; set; }
        
        /// <summary>
        /// The date when the customer was created
        /// </summary>
        [JsonPropertyName("created_date")]
        [Description("The date when the customer was created")]
        public DateTime CreatedDate { get; set; }
        
        /// <summary>
        /// The reward points of the customer
        /// </summary>
        [JsonPropertyName("reward_point")]
        [Description("The reward points of the customer")]
        public decimal? RewardPoint { get; set; }
        
        /// <summary>
        /// The groups the customer belongs to
        /// </summary>
        [JsonPropertyName("customer_groups")]
        [Description("The groups the customer belongs to")]
        public List<CustomerGroupResponse>? CustomerGroups { get; set; }
    }
} 