﻿using KvFnB.Modules.Payment.Application.UseCases.BankAccountUseCases.CreateBankAccountUseCase;
using KvFnB.Modules.Payment.Application.UseCases.Contracts;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Payment.Application.UseCases.BankAccountUseCases.GetBankAccountUseCase
{
    public class GetBankAccountResponse
    {
        [JsonPropertyName("id"), Description("The unique identifier of the bank account")]
        public int Id { get; set; }

        [JsonPropertyName("bank"), Description("The bank name"), JsonIgnore(Condition = JsonIgnoreCondition.Never)]
        public string? Bank { get; set; }

        [JsonPropertyName("account"), Description("The account number"), JsonIgnore(Condition = JsonIgnoreCondition.Never)]
        public string? Account { get; set; }

        [JsonPropertyName("account_name"), Description("The account holder name"), JsonIgnore(Condition = JsonIgnoreCondition.Never)]
        public string? AccountName { get; set; }

        [JsonPropertyName("bank_code"), Description("The bank code"), JsonIgnore(Condition = JsonIgnoreCondition.Never)]
        public string? BankCode { get; set; }

        [JsonPropertyName("bank_name"), Description("The bank name"), JsonIgnore(Condition = JsonIgnoreCondition.Never)]
        public string? BankName { get; set; }

        [JsonPropertyName("branch"), Description("The bank branch name"), JsonIgnore(Condition = JsonIgnoreCondition.Never)]
        public string? Branch { get; set; }

        [JsonPropertyName("description"), Description("Additional description"), JsonIgnore(Condition = JsonIgnoreCondition.Never)]
        public string? Description { get; set; }


        [JsonPropertyName("is_default"), Description("Whether this is the default bank account")]
        public bool IsDefault { get; set; }

        [JsonPropertyName("is_deleted"), Description("Whether this bank account is deleted")]
        public bool IsDeleted { get; set; }

        [JsonPropertyName("bank_account_branches"), Description("List of branches associated with this bank account")]
        public List<BankAccountBranchDto> BankAccountBranches { get; set; } = [];

        [JsonPropertyName("image_url"), Description("image_url")]
        public string? ImageUrl { get; set; }
    }
}
