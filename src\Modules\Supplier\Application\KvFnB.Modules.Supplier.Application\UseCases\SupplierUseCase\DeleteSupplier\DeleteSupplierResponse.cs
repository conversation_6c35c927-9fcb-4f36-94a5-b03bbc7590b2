﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeleteSupplier
{
    public record DeleteSupplierResponse
    {
        [JsonPropertyName("id"), Description("The ID of the deleted supplier.")]
        public int Id { get; init; }

        [JsonPropertyName("code"), Description("The code of the deleted supplier.")]
        public string Code { get; init; } = string.Empty;

        [JsonPropertyName("phone"), Description("The phone of the deleted supplier.")]
        public string Phone { get; init; } = string.Empty;

        [JsonPropertyName("name"), Description("The name of the deleted supplier.")]
        public string Name { get; init; } = string.Empty;

        [JsonPropertyName("is_deleted"), Description("Indicates whether the supplier is deleted.")]
        public bool IsDeleted { get; init; }
    }
}
