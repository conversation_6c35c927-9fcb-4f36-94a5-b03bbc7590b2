﻿using KvFnB.Core.Abstractions;
using KvFnB.Core.Constants;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.ActiveSupplier;
using KvFnB.Modules.Supplier.Domain.Repositories;
using KvFnB.Modules.Supplier.Domain.Specifications;

namespace KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeActiveSupplier
{
    public class DeActiveSupplierUseCase : UseCaseBase<DeActiveSupplierRequest, DeActiveSupplierResponse>
    {
        private readonly IValidator<DeActiveSupplierRequest> _validator;
        private readonly ISupplierRepository _supplierRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="ActiveSupplierUseCase"/> class.
        /// </summary>
        /// <param name="validator">The validator for the request.</param>
        /// <param name="supplierRepository">The repository for suppliers.</param>
        /// <param name="unitOfWork">The unit of work for transaction management.</param>
        /// <param name="logger">The logger for logging information.</param>
        public DeActiveSupplierUseCase(
            IValidator<DeActiveSupplierRequest> validator,
            ISupplierRepository supplierRepository,
            IUnitOfWork unitOfWork,
            ILogger logger)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _supplierRepository = supplierRepository ?? throw new ArgumentNullException(nameof(supplierRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Executes the use case to activate a supplier.
        /// </summary>
        /// <param name="request">The request containing the supplier ID to activate.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A result containing the response or error messages.</returns>
        public override async Task<Result<DeActiveSupplierResponse>> ExecuteAsync(
            DeActiveSupplierRequest request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                // 1. Validate request
                var validationResult = _validator.Validate(request);
                if (!validationResult.IsValid)
                {
                    return Result<DeActiveSupplierResponse>.Failure(validationResult.Errors);
                }

                // 2. Get the supplier from the repository using specification
                var supplier = await _supplierRepository.GetAsync(new SupplierByIdSpecification(request.SupplierId), cancellationToken);

                if (supplier == null)
                {
                    return Result<DeActiveSupplierResponse>.Failure("Nhà cung cấp không tồn tại hoặc đã bị xóa khỏi hệ thống");
                }

                // Check if the supplier is already deactive
                if (!supplier.IsActive)
                {
                    // Map response and return success - no need to update
                    var alreadyDeActiveResponse = new DeActiveSupplierResponse
                    {
                        Id = supplier.Id,
                        Name = supplier.Name ?? string.Empty,
                        IsActive = supplier.IsActive
                    };
                    return Result<DeActiveSupplierResponse>.Success(alreadyDeActiveResponse);
                }

                // 3. Deactivate the supplier
                supplier.UpdateSupplierDeActive();

                // 4. Update the supplier in the repository
                await _supplierRepository.UpdateAsync(supplier, cancellationToken);

                // 5. Commit the changes
                await _unitOfWork.CommitAsync(cancellationToken);

                // 6. Map to response and return success
                var response = new DeActiveSupplierResponse
                {
                    Id = supplier.Id,
                    Name = supplier.Name ?? string.Empty,
                    IsActive = supplier.IsActive
                };

                return Result<DeActiveSupplierResponse>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.Error(string.Format("Error occurred while deactivating supplier with ID {0}", request.SupplierId),ex);
                return Result<DeActiveSupplierResponse>.Failure(ErrorMessages.InternalServerError);
            }
        }
    }
}
