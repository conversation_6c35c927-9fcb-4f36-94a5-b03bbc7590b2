using KvFnB.Core.Domain;
namespace KvFnB.Modules.Customer.Domain.Models;

public class CustomerGroupDetail : Entity<long>
{
    public long CustomerId { get; set; }
    public int GroupId { get; set; }
    public DateTime CreatedDate  { get ; set; }
    public Customer? Customer { get; set; }

    private CustomerGroupDetail() { }

    private CustomerGroupDetail(long customerId, int groupId)
    {
        CustomerId = customerId;
        GroupId = groupId;
        CreatedDate = DateTime.Now;
    }

    public static CustomerGroupDetail Create(long customerId, int groupId)
    {
        return new CustomerGroupDetail(customerId, groupId);
    }

}   