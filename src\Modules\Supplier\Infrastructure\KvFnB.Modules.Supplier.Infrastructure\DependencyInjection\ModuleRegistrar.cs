﻿using KvFnB.Core.Validation;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.ActiveSupplier;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeActiveSupplier;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeleteSupplier;
using KvFnB.Modules.Supplier.Domain.Repositories;
using KvFnB.Modules.Supplier.Infrastructure.Persistence;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace KvFnB.Modules.Supplier.Infrastructure.DependencyInjection
{
    public static class ModuleRegistrar
    {
        public static void AddSupplierModule(this IServiceCollection services, IConfiguration configuration)
        {
            // Register use cases
            services.AddScoped<ActiveSupplierUseCase>();
            services.AddScoped<DeActiveSupplierUseCase>();
            services.AddScoped<DeleteSupplierUseCase>();

            // Register repositories
            services.AddScoped<ISupplierRepository, SupplierRepository>();


            // Register validators
            services.AddScoped<IValidator<ActiveSupplierRequest>, ActiveSupplierValidator>();
            services.AddScoped<IValidator<DeActiveSupplierRequest>, DeActiveSupplierValidator>();
            services.AddScoped<IValidator<DeleteSupplierRequest>, DeleteSupplierValidator>();
        }
    }
}
