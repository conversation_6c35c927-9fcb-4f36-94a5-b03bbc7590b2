using KvFnB.Core.Domain;

namespace KvFnB.Modules.Customer.Domain.Models;

public class Customer: Entity<long>, IAuditableEntity, ISoftDeletableEntity, ICode
{
    private readonly string SuffixDeleted = "{DEL}";

    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? ContactNumber { get; set; } = string.Empty;
    public bool? Gender { get; set; }
    public string? Email { get; set; } = string.Empty;
    public string? Avatar { get; set; } = string.Empty;
    public string? Comments { get; set; } = string.Empty;
    public string? Address { get; set; } = string.Empty;
    public string? LocationName { get; set; } = string.Empty;
    public string? WardName { get; set; } = string.Empty;
    public int? LocationId { get; set; }
    public bool IsActive { get; set; }
    public int? BranchId { get; set; }
    public DateTime? BirthDate { get; set; }
    public byte Type { get; set; }
    public string? Organization { get; set; } = string.Empty;
    public string? TaxCode { get; set; } = string.Empty;
    public decimal? Debt { get; set; }
    public long? RewardPoint { get; set; }
    public long? AddressId { get; set; }
    public int[]? GroupIds { get; set; }
    public DateTime CreatedAt { get; set; } 
    public long CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public long? ModifiedBy { get; set; }
    public bool? IsDeleted { get; set; }
    public DateTime? DeletedAt { get; set; }
    public long? DeletedBy { get; set; }

	public ICollection<CustomerGroupDetail>? CustomerGroupDetails { get; set; }

    public void UpdateAddress(long addressId, string addressLine)
    {
        AddressId = addressId;
        Address = addressLine;
    }

    public void UpdateCustomerGroupDetails()
    {
        CustomerGroupDetails = GroupIds?.Select(groupId => CustomerGroupDetail.Create(Id, groupId)).ToList();
    }

    public void Delete()
    {
        IsDeleted = true;
        Name += SuffixDeleted;
    }

    public void UpdateCode(string code)
    {
        Code = code;
    }

    public void Activate(bool isActive)
    {
        IsActive = isActive;
    }
} 