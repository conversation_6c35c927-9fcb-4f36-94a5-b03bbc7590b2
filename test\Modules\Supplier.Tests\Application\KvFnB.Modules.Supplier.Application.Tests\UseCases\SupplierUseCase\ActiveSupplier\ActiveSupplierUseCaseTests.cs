﻿using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.ActiveSupplier;
using KvFnB.Modules.Supplier.Domain.Repositories;
using KvFnB.Modules.Supplier.Domain.Specifications;
using Moq;
using Xunit;
using Assert = Xunit.Assert;

namespace KvFnB.Modules.Supplier.Application.Tests.UseCases.SupplierUseCase.ActiveSupplier
{
    public class ActiveSupplierUseCaseTests
    {
        private readonly Mock<IValidator<ActiveSupplierRequest>> _validatorMock;
        private readonly Mock<ISupplierRepository> _repositoryMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<ILogger> _loggerMock;

        private readonly ActiveSupplierUseCase _useCase;

        public ActiveSupplierUseCaseTests()
        {
            _validatorMock = new Mock<IValidator<ActiveSupplierRequest>>();
            _repositoryMock = new Mock<ISupplierRepository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger>();

            _useCase = new ActiveSupplierUseCase(
                _validatorMock.Object,
                _repositoryMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object
            );

            _validatorMock.Setup(v => v.Validate(It.IsAny<ActiveSupplierRequest>()))
                .Returns(ValidationResult.Success());
        }

        [Fact]
        public async Task ExecuteAsync_ShouldReturnValidationFailure_WhenInvalidRequest()
        {
            var request = new ActiveSupplierRequest { SupplierId = 0 };
            var validationResult = ValidationResult.Failure(new List<string> { "SupplierId must be greater than 0" });

            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            var result = await _useCase.ExecuteAsync(request);

            Assert.False(result.IsSuccess);
            Assert.Equal(validationResult.Errors, result.ValidationErrors);
            _repositoryMock.Verify(r => r.GetAsync(It.IsAny<SupplierByIdSpecification>(), It.IsAny<CancellationToken>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_ShouldReturnFailure_WhenSupplierNotFound()
        {
            var request = new ActiveSupplierRequest { SupplierId = 1 };

            _repositoryMock.Setup(r => r.GetAsync(It.IsAny<SupplierByIdSpecification>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((Domain.Models.Supplier)null!);

            var result = await _useCase.ExecuteAsync(request);

            Assert.False(result.IsSuccess);
            Assert.Contains("không tồn tại", result.ErrorMessage);
        }

        [Fact]
        public async Task ExecuteAsync_ShouldReturnSuccess_WhenSupplierAlreadyActive()
        {
            var request = new ActiveSupplierRequest { SupplierId = 1 };
            var supplier = new Domain.Models.Supplier { Id = 1, IsActive = true };

            _repositoryMock.Setup(r => r.GetAsync(It.IsAny<SupplierByIdSpecification>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(supplier);

            var result = await _useCase.ExecuteAsync(request);

            Assert.True(result.IsSuccess);
            Assert.Equal(supplier.Id, result.Value!.Id);
            Assert.True(result.Value.IsActive);
        }

        [Fact]
        public async Task ExecuteAsync_ShouldActivateSupplier_WhenSupplierIsInactive()
        {
            var request = new ActiveSupplierRequest { SupplierId = 1 };
            var supplier = new Domain.Models.Supplier { Id = 1, IsActive = false };

            _repositoryMock.Setup(r => r.GetAsync(It.IsAny<SupplierByIdSpecification>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(supplier);

            _repositoryMock.Setup(r => r.UpdateAsync(supplier, It.IsAny<CancellationToken>()))
                .ReturnsAsync(supplier);

            _unitOfWorkMock.Setup(u => u.CommitAsync(It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            var result = await _useCase.ExecuteAsync(request);

            Assert.True(result.IsSuccess);
            Assert.Equal(supplier.Id, result.Value!.Id);
            Assert.True(supplier.IsActive);

            _repositoryMock.Verify(r => r.UpdateAsync(supplier, It.IsAny<CancellationToken>()), Times.Once);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_ShouldReturnFailure_WhenExceptionOccurs()
        {
            var request = new ActiveSupplierRequest { SupplierId = 1 };

            _repositoryMock.Setup(r => r.GetAsync(It.IsAny<SupplierByIdSpecification>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("DB error"));

            var result = await _useCase.ExecuteAsync(request);

            Assert.False(result.IsSuccess);
            Assert.Contains("Internal", result.ErrorMessage);
        }
    }
}
