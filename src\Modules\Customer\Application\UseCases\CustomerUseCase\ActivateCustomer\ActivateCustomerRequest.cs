using System.ComponentModel;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.ActivateCustomer
{
    /// <summary>
    /// Request model for activating a customer
    /// </summary>
    public class ActivateCustomerRequest
    {
        /// <summary>
        /// The ID of the customer to activate
        /// </summary>
        [JsonPropertyName("customer_id"), Description("The unique identifier of the customer to activate.")]
        public long CustomerId { get; init; }
    }
} 