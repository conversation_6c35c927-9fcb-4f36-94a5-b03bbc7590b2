﻿using KvFnB.Core.Validation;
using KvFnB.Core.Abstractions;
using KvFnB.Modules.Supplier.Domain.Repositories;
using Moq;
using Xunit;
using Assert = Xunit.Assert;
using KvFnB.Modules.Supplier.Domain.Specifications;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeActiveSupplier;

namespace KvFnB.Modules.Supplier.Application.Tests.UseCases.SupplierUseCase.DeActiveSupplier
{
    public class DeActiveSupplierUseCaseTests
    {
        private readonly Mock<IValidator<DeActiveSupplierRequest>> _validatorMock;
        private readonly Mock<ISupplierRepository> _repositoryMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<ILogger> _loggerMock;

        private readonly DeActiveSupplierUseCase _useCase;

        public DeActiveSupplierUseCaseTests()
        {
            _validatorMock = new Mock<IValidator<DeActiveSupplierRequest>>();
            _repositoryMock = new Mock<ISupplierRepository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger>();

            _useCase = new DeActiveSupplierUseCase(
                _validatorMock.Object,
                _repositoryMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object
            );

            _validatorMock.Setup(v => v.Validate(It.IsAny<DeActiveSupplierRequest>()))
                .Returns(ValidationResult.Success());
        }

        [Fact]
        public async Task ExecuteAsync_ShouldReturnValidationFailure_WhenInvalidRequest()
        {
            var request = new DeActiveSupplierRequest { SupplierId = 0 };
            var validationResult = ValidationResult.Failure(new List<string> { "SupplierId must be greater than 0" });

            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            var result = await _useCase.ExecuteAsync(request);

            Assert.False(result.IsSuccess);
            Assert.Equal(validationResult.Errors, result.ValidationErrors);
            _repositoryMock.Verify(r => r.GetAsync(It.IsAny<SupplierByIdSpecification>(), It.IsAny<CancellationToken>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_ShouldReturnFailure_WhenSupplierNotFound()
        {
            var request = new DeActiveSupplierRequest { SupplierId = 1 };

            _repositoryMock.Setup(r => r.GetAsync(It.IsAny<SupplierByIdSpecification>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((Domain.Models.Supplier)null!);

            var result = await _useCase.ExecuteAsync(request);

            Assert.False(result.IsSuccess);
            Assert.Contains("không tồn tại", result.ErrorMessage);
        }

        [Fact]
        public async Task ExecuteAsync_ShouldReturnSuccess_WhenSupplierAlreadyDeActive()
        {
            var request = new DeActiveSupplierRequest { SupplierId = 1 };
            var supplier = new Domain.Models.Supplier { Id = 1, IsActive = false };

            _repositoryMock.Setup(r => r.GetAsync(It.IsAny<SupplierByIdSpecification>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(supplier);

            var result = await _useCase.ExecuteAsync(request);

            Assert.True(result.IsSuccess);
            Assert.Equal(supplier.Id, result.Value!.Id);
            Assert.False(result.Value.IsActive);
        }

        [Fact]
        public async Task ExecuteAsync_ShouldActivateSupplier_WhenSupplierIsActive()
        {
            var request = new DeActiveSupplierRequest { SupplierId = 1 };
            var supplier = new Domain.Models.Supplier { Id = 1, IsActive = true };

            _repositoryMock.Setup(r => r.GetAsync(It.IsAny<SupplierByIdSpecification>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(supplier);

            _repositoryMock.Setup(r => r.UpdateAsync(supplier, It.IsAny<CancellationToken>()))
                .ReturnsAsync(supplier);

            _unitOfWorkMock.Setup(u => u.CommitAsync(It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            var result = await _useCase.ExecuteAsync(request);

            Assert.True(result.IsSuccess);
            Assert.Equal(supplier.Id, result.Value!.Id);
            Assert.False(supplier.IsActive);

            _repositoryMock.Verify(r => r.UpdateAsync(supplier, It.IsAny<CancellationToken>()), Times.Once);
            _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_ShouldReturnFailure_WhenExceptionOccurs()
        {
            var request = new DeActiveSupplierRequest { SupplierId = 1 };

            _repositoryMock.Setup(r => r.GetAsync(It.IsAny<SupplierByIdSpecification>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("DB error"));

            var result = await _useCase.ExecuteAsync(request);

            Assert.False(result.IsSuccess);
            Assert.Contains("Internal", result.ErrorMessage);
        }

    }
}
