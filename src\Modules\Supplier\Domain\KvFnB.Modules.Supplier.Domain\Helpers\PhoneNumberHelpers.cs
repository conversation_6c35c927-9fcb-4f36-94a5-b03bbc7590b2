﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace KvFnB.Modules.Supplier.Domain.Helpers
{
    public static class PhoneNumberHelpers
    {
        public static string GetPerfectContactNumber(string inputNumber)
        {
            if (string.IsNullOrWhiteSpace(inputNumber))
            {
                return string.Empty;
            }

            string pattern = "\\s|\\-";
            inputNumber = inputNumber.Normalize().Trim();
            inputNumber = Regex.Replace(inputNumber, pattern, string.Empty, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            return inputNumber;
        }
    }
}
