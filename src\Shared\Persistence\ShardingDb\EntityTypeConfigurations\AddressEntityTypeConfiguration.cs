using KvFnB.Core.Domain.Models;
using KvFnB.Modules.Customer.Domain.Models;
using KvFnB.Shared.Persistence.ShardingDb.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KvFnB.Shared.Persistence.ShardingDb.EntityTypeConfigurations
{
    public class AddressEntityTypeConfiguration : BaseEntityTypeConfiguration<Address>
    {
        public override void Configure(EntityTypeBuilder<Address> builder)
        {
            base.Configure(builder);
            builder.ToTable("Address");

            builder.Property(p => p.TenantId)
               .IsRequired(true)
               .HasColumnName("RetailerId")
               .HasColumnType(SqlServerColumnTypes.INT);
        }
    }
}