﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeleteSupplier
{
    public record DeleteSupplierRequest
    {
        /// <summary>
        /// The ID of the supplier to delete
        /// </summary>
        [JsonPropertyName("supplier_id"), Description("The ID of the supplier to delete.")]
        public int SupplierId { get; init; }
    }
}
