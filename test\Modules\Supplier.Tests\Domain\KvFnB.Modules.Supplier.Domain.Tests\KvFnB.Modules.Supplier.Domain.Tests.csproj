<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UseVSTest>true</UseVSTest>
  </PropertyGroup>
	
	<ItemGroup>
		<PackageReference Include="coverlet.collector" Version="6.0.0" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
		<PackageReference Include="Moq" Version="4.20.72" />
		<PackageReference Include="xunit" Version="2.5.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.5.3" />
	</ItemGroup>
	
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\src\Modules\Supplier\Domain\KvFnB.Modules.Supplier.Domain\KvFnB.Modules.Supplier.Domain.csproj" />
  </ItemGroup>

</Project>
