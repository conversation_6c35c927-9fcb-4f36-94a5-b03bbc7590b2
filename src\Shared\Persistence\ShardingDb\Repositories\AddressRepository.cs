using System.Linq;
using KvFnB.Core.Domain.Models;
using KvFnB.Core.Domain.Repositories;
using KvFnB.Core.Domain;
using Microsoft.EntityFrameworkCore;

namespace KvFnB.Shared.Persistence.ShardingDb.Repositories
{
    /// <summary>
    /// Implementation of the address repository using Entity Framework Core
    /// </summary>
    public class AddressRepository : BaseRepository<Address, long>, IAddressRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="AddressRepository"/> class
        /// </summary>
        /// <param name="context">The database context</param>
        public AddressRepository(ShardingDbContext context) : base(context)
        {
        }

    }
} 