using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;
using KvFnB.Shared.Persistence.ShardingDb;
using Microsoft.EntityFrameworkCore;

namespace KvFnB.Modules.Menu.Infrastructure.Persistence
{
    public class NoteTemplateRepository : BaseRepository<NoteTemplate, long>, INoteTemplateRepository
    {
        public NoteTemplateRepository(ShardingDbContext context) : base(context)
        {
        }

        public async Task<List<NoteTemplate>> GetByGroupIdAsync(long groupId, CancellationToken cancellationToken = default)
        {
            return await _context.Set<NoteTemplate>()
                .Where(x => x.GroupId == groupId && (x.IsDeleted == false || x.IsDeleted == null))
                .ToListAsync(cancellationToken);
        }

        public async Task<bool> IsUniqueNoteTemplateNameAsync(string name, long? excludeNoteTemplateId = null, CancellationToken cancellationToken = default)
        {
            var query = _context.Set<NoteTemplate>().Where(x => x.Name == name && (x.IsDeleted == false || x.IsDeleted == null));

            if (excludeNoteTemplateId.HasValue)
            {
                query = query.Where(x => x.Id != excludeNoteTemplateId.Value);
            }

            return !await query.AnyAsync(cancellationToken);
        }
    }
} 