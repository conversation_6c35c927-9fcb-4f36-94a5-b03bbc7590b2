using System.ComponentModel;
using System.Text.Json.Serialization;
using System.Collections.Generic;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.CreateCustomer
{
    /// <summary>
    /// Represents the request model for the CreateCustomer use case
    /// </summary>
    public class CreateCustomerRequest
    {
        /// <summary>
        /// The unique code for the customer
        /// </summary>
        [JsonPropertyName("code")]
        [Description("The unique code for the customer")]
        public string Code { get; set; } = string.Empty;
        
        /// <summary>
        /// The name of the customer
        /// </summary>
        [JsonPropertyName("name")]
        [Description("The name of the customer")]
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// The contact number of the customer
        /// </summary>
        [JsonPropertyName("contact_number")]
        [Description("The contact number of the customer")]
        public string? ContactNumber { get; set; }
        
        /// <summary>
        /// The gender of the customer (true for male, false for female)
        /// </summary>
        [JsonPropertyName("gender")]
        [Description("The gender of the customer (true for male, false for female)")]
        public bool? Gender { get; set; }
        
        /// <summary>
        /// The email address of the customer
        /// </summary>
        [JsonPropertyName("email")]
        [Description("The email address of the customer")]
        public string? Email { get; set; }
        
        /// <summary>
        /// The URL of the customer's avatar image
        /// </summary>
        [JsonPropertyName("avatar")]
        [Description("The URL of the customer's avatar image")]
        public string? Avatar { get; set; }
        
        /// <summary>
        /// Additional comments about the customer
        /// </summary>
        [JsonPropertyName("comments")]
        [Description("Additional comments about the customer")]
        public string? Comments { get; set; }
        
        /// <summary>
        /// The address of the customer
        /// </summary>
        [JsonPropertyName("address")]
        [Description("The address of the customer")]
        public string? Address { get; set; }
        
        /// <summary>
        /// The name of the customer's location
        /// </summary>
        [JsonPropertyName("location_name")]
        [Description("The name of the customer's location")]
        public string? LocationName { get; set; }
        
        /// <summary>
        /// The name of the customer's ward
        /// </summary>
        [JsonPropertyName("ward_name")]
        [Description("The name of the customer's ward")]
        public string? WardName { get; set; }
        
        /// <summary>
        /// The ID of the customer's location
        /// </summary>
        [JsonPropertyName("location_id")]
        [Description("The ID of the customer's location")]
        public int? LocationId { get; set; }
        
        /// <summary>
        /// Whether the customer is active
        /// </summary>
        [JsonPropertyName("is_active")]
        [Description("Whether the customer is active")]
        public bool IsActive { get; set; } = true;
        
        /// <summary>
        /// The ID of the branch associated with the customer
        /// </summary>
        [JsonPropertyName("branch_id")]
        [Description("The ID of the branch associated with the customer")]
        public int? BranchId { get; set; }
        
        /// <summary>
        /// The birth date of the customer
        /// </summary>
        [JsonPropertyName("birth_date")]
        [Description("The birth date of the customer")]
        public DateTime? BirthDate { get; set; }
        
        /// <summary>
        /// The type of customer (1: Individual, 2: Corporate)
        /// </summary>
        [JsonPropertyName("type")]
        [Description("The type of customer (1: Individual, 2: Corporate)")]
        public byte Type { get; set; } = 1;
        
        /// <summary>
        /// The customer's organization name (for corporate customers)
        /// </summary>
        [JsonPropertyName("organization")]
        [Description("The customer's organization name (for corporate customers)")]
        public string? Organization { get; set; }
        
        /// <summary>
        /// The tax code of the customer or organization
        /// </summary>
        [JsonPropertyName("tax_code")]
        [Description("The tax code of the customer or organization")]
        public string? TaxCode { get; set; }
        
        /// <summary>
        /// IDs of customer groups this customer belongs to
        /// </summary>
        [JsonPropertyName("group_ids")]
        [Description("IDs of customer groups this customer belongs to")]
        public List<int>? GroupIds { get; set; }

        /// <summary>
        /// The address of the customer
        /// </summary>
        [JsonPropertyName("address_location")]
        [Description("The address of the customer")]
        public AddressRequest? AddressLocation { get; set; }
    }

    public class AddressRequest
    {
        [JsonPropertyName("address_line")]
        [Description("The address line of the address")]
        public string? AddressLine { get; set; } = string.Empty;

        [JsonPropertyName("administrative_area_id")]
        [Description("The ID of the administrative area of the address")]
        public int AdministrativeAreaId { get; set; }

        [JsonPropertyName("postal_code")]
        [Description("The postal code of the address")]
        public string? PostalCode { get; set; } = string.Empty;
    }
} 