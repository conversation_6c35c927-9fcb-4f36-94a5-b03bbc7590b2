using Azure;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Models;
using Microsoft.Extensions.Configuration;
using RestSharp;
using System.Net;

namespace KvFnB.Shared.KmaService
{
    /// <summary>
    /// Implementation of IKmaService for interacting with KMA APIs
    /// </summary>
    public class KmaService : IKmaService
    {
        private readonly ILogger _logger;
        private readonly KmaConfiguration _kmaConfiguration;
        private readonly IConfiguration _appConfiguration;

        /// <summary>
        /// Constructor for KmaService
        /// </summary>
        public KmaService(
            KmaConfiguration configuration, 
            ILogger logger, 
            IConfiguration appConfiguration)
        {
            _kmaConfiguration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _appConfiguration = appConfiguration ?? throw new ArgumentNullException(nameof(appConfiguration));
        }

        /// <summary>
        /// Verifies a phone number through KMA API
        /// </summary>
        public async Task<bool> VerifyPhoneNumberAsync(int tenantId, string phoneNumber, CancellationToken cancellationToken = default)
        {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

                // Hardcoded values from curl command
                var baseUrl = _kmaConfiguration.CmaOpenDomainEndpoint;
                var apiKey = _kmaConfiguration.ApiKey;

                // Create client and request
                var client = CreateRestClient(baseUrl, _kmaConfiguration.Timeout);
                var request = new RestRequest("/v1/contracts/verifyContractPhone", Method.POST);

                // Add headers exactly as in curl command
                request.AddHeader("content-type", "application/json");
                request.AddHeader("X-API-KEY", apiKey);

                // Add body exactly as in curl command
                var requestBody = new
                {
                    phone = phoneNumber,
                    retailer_id = tenantId
                };
                request.AddJsonBody(requestBody);

                // Execute request
                var response = await client.ExecutePostTaskAsync<KmaVerifyPhoneNumberApiResponse>(request, cancellationToken);

                // Check for HTTP errors
                if (response.StatusCode != HttpStatusCode.OK)
                {
                    _logger.Error($"KMA API error: {response.ErrorMessage} - {response.Content}");
                    return false;
                }

                return response.Data.IsValid;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error verifying phone number with KMA: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// Checks the digital signature status for a merchant
        /// </summary>
        public async Task<DigitalSignatureResponse> CheckDigitalSignatureAsync(int retailerId, CancellationToken cancellationToken = default)
        {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

                var baseUrl = _kmaConfiguration.CmaOpenDomainEndpoint;
                var apiKey = _kmaConfiguration.ApiKey;

                var client = CreateRestClient(baseUrl, _kmaConfiguration.Timeout);
                var request = new RestRequest($"/v1/invoices/byRetailerId/{retailerId}/digitalSignatureStatus", Method.GET);

                request.AddHeader("X-API-KEY", apiKey);

                var response = await client.ExecuteGetTaskAsync<DigitalSignatureResponse>(request, cancellationToken);

                if (response.StatusCode != HttpStatusCode.OK)
                {
                   throw new Exception($"KMA API error: {response.ErrorMessage} - {response.Content}");
                }

                return response.Data;
            }
            catch (HttpRequestException ex)
            {
                throw new Exception("KMA API error", ex);
            }
        }

        /// <summary>
        /// Gets the e-invoice quota for a merchant
        /// </summary>
        public async Task<KmaEInvoiceQuota> GetEInvoiceQuotaAsync(string taxCode, int retailerId, CancellationToken cancellationToken = default)
        {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

                var baseUrl = _kmaConfiguration.CmaOpenDomainEndpoint;
                var apiKey = _kmaConfiguration.ApiKey;

                var client = CreateRestClient(baseUrl, _kmaConfiguration.Timeout);
                var request = new RestRequest("/v1/invoices/quota", Method.GET);

                request.AddQueryParameter("tax_code", taxCode);
                request.AddQueryParameter("retailer_id", retailerId.ToString());
                request.AddHeader("X-API-KEY", apiKey);

                var response = await client.ExecuteGetTaskAsync<KmaEInvoiceQuota>(request, cancellationToken);

                if (response.StatusCode != HttpStatusCode.OK)
                {
                    throw new Exception($"KMA API error: {response.ErrorMessage} - {response.Content}");
                }

                return response.Data;
            }
            catch (Exception ex)
            {
                throw new Exception("KMA API error", ex);
            }
        }

        /// <summary>
        /// Creates a configured REST client
        /// </summary>
        private RestClient CreateRestClient(string baseUrl, int timeoutInMilliseconds)
        {
            //var proxy = _appConfiguration["ProxyUrl"];
            //if (!string.IsNullOrEmpty(proxy))
            //{
            //    return new RestClient(baseUrl)
            //    {
            //        Timeout = timeoutInMilliseconds,
            //        Proxy = GetProxy(proxy)
            //    };
            //}

            return new RestClient(baseUrl)
            {
                Timeout = timeoutInMilliseconds
            };
        }

        /// <summary>
        /// Creates a web proxy from a URL
        /// </summary>
        private static WebProxy GetProxy(string proxyUrl)
        {
            return new WebProxy
            {
                Address = new Uri(proxyUrl)
            };
        }

    }

} 