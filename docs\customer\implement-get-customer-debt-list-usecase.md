Please generate the complete 'Get List Customer Debt' feature for the `Customer` module within the `KvFnB Core` solution. This includes the API endpoint, use case, DTOs, validator, mappings, dependency injection registration, and unit tests.

Ensure the generated code strictly adheres to the solution's architecture and guidelines defined in:
*   `ARCHITECTURE.md` (Hexagonal architecture, module structure: `src/Modules/Customer/`, layers, use of Dapper via `IQueryService` for queries)
*   `usecase_implementation.mdc` (Required components: Request, Response, Validator, UseCase, DI, Mapping, Tests; follow Query Use Case template structure)
*   `coding_convention.mdc` (Parameter limits, complexity, API documentation using `[Description]` and `[JsonPropertyName]`)
*   `comment_code.mdc` (Clear, educational comments explaining the 'why' and complex logic)
*   `test_code.mdc` (AAA pattern, BDD-style naming, comprehensive unit tests covering success, validation failure, and specific logic paths)
*   `error_handling.mdc` (Use standardized error messages from `ErrorMessages` constants in UseCase failure results)

**Feature Requirements:**

1.  **API Endpoint:**
    *   Create an ASP.NET Core API Controller `CustomerDebtsController` in `src/Modules/Customer/Presentation/Restful/`.
    *   Endpoint: `GET /v2/customers/{customerId}/debts` (Use `[HttpGet("{customerId}/debts")]`)
    *   Path Parameter: `long customerId` (`[FromRoute]`)
    *   Query Parameters: `int skip = 0`, `int take = 10`, `DateTime? startDate`, `DateTime? endDate` (`[FromQuery]`).
    *   Permissions: Add necessary authorization attribute(s) requiring `Customer._Read` permission.
    *   Response: `ActionResult<GetListCustomerDebtResponse>` (defined below).
    *   Follow API documentation conventions from `coding_convention.mdc` for request parameters and response model properties using XML comments or Swagger attributes.

2.  **Use Case Components (Place within `src/Modules/Customer/Application/UseCases/CustomerDebtUseCase/GetListCustomerDebt/` unless specified otherwise):**
    *   **`GetListCustomerDebtRequest.cs`**: A record containing `long CustomerId`, `int Skip`, `int Take`, `DateTime? StartDate`, `DateTime? EndDate`. Add descriptive XML comments.
    *   **`CustomerDebtDto.cs` (in `src/Modules/Customer/Application/Contracts/`)**: A record representing a single debt entry. Properties: `string DocumentCode`, `PaymentDocTypes DocumentType` (assuming this enum exists), `long DocumentId`, `DateTime TransDate`, `decimal Balance`, `decimal Value`. Add descriptive XML comments and `[JsonPropertyName]` attributes following `coding_convention.mdc`.
    *   **`GetListCustomerDebtResponse.cs`**: A record containing `long TotalItems`, `IReadOnlyList<CustomerDebtDto> Items`, `decimal TotalDebt`. Add descriptive XML comments and `[JsonPropertyName]` attributes.
    *   **`GetListCustomerDebtValidator.cs`**: Inherit from `Validator<GetListCustomerDebtRequest>`. Validate `CustomerId` > 0, `Skip` >= 0, `Take` > 0 (e.g., max 100). Use methods from `KvFnB.Core.Validation.Extensions`.
    *   **`GetListCustomerDebtUseCase.cs`**:
        *   Implement as a Query Use Case inheriting `UseCaseBase<GetListCustomerDebtRequest, GetListCustomerDebtResponse>`.
        *   Inject `IValidator<GetListCustomerDebtRequest>`, `IQueryService`, `IMapper`, `ITenantProvider`, `IMultiCurrencyLegacyService`, and a service/repository interface (e.g., `ICustomerReadRepository` or similar) to get the customer's total debt.
        *   **Logic:**
            *   (Validation is handled by `UseCaseBase`).
            *   Retrieve the customer's total debt using the injected service/repository and `request.CustomerId`. Handle cases where the customer might not be found.
            *   Construct a `QueryBuilder` query for the `BalanceTracking` table via `IQueryService`.
            *   **Filtering:** Where `TenantId` (from `ITenantProvider`), `PartnerId` = `request.CustomerId`, `DataZone` = `BalanceTrackTypes.Customer`, `Value` != 0. Add `TransDate` >= `request.StartDate` and `TransDate` < `request.EndDate` if they have values.
            *   **Pagination/Grouping:**
                *   Build a subquery/CTE to select distinct `DocumentCode` based on filters, ordered by `MAX(TransDate) DESC`.
                *   Apply `Skip` and `Take` to this subquery to get the paginated `DocumentCode`s.
                *   Calculate the total count of distinct `DocumentCode`s matching the filters (without pagination).
            *   **Data Retrieval:** Select all `BalanceTracking` rows where `DocumentCode` is in the paginated list of codes and other filters match.
            *   **Aggregation (In Memory):** Group the retrieved `BalanceTracking` rows by `DocumentCode`. For each group: calculate `Sum(Value)`, find the entry with the latest `TransDate` (and highest `DocumentId` as tie-breaker) to get the final `Balance`, `DocumentType`, `DocumentId`, and `TransDate`.
            *   **Rounding:** Use `IMultiCurrencyLegacyService.Round()` on the calculated `Balance` and `Value` for each aggregated item.
            *   **Mapping:** Map the aggregated results to `List<CustomerDebtDto>`.
            *   **Response:** Construct the `GetListCustomerDebtResponse` with the calculated `TotalItems`, the mapped `Items` list (ordered by `TransDate DESC`), and the previously fetched `TotalDebt`.
            *   Return `Result<GetListCustomerDebtResponse>.Success(...)` or `Result.Failure(...)` using constants from `ErrorMessages` for known failures (e.g., customer not found, internal server error).

3.  **Mappings (in `src/Modules/Customer/Infrastructure/Mapping/CustomerMappingProfile.cs`):**
    *   Add necessary AutoMapper mappings, particularly from the aggregated data structure (likely a temporary class or tuple) to `CustomerDebtDto`. Ensure `CustomerMappingProfile` is registered.

4.  **Dependency Injection (in `src/Modules/Customer/Infrastructure/DependencyInjection/ModuleRegistrar.cs` or equivalent):**
    *   Register `GetListCustomerDebtUseCase` (as its own type or `IUseCase<...>`), `IValidator<GetListCustomerDebtRequest>` to `GetListCustomerDebtValidator`, and any new repository/service interfaces injected into the use case. Ensure the `RegisterCustomerModule` method is called during startup.

5.  **Unit Tests (in `src/Modules/Customer/Tests/Application/UseCases/CustomerDebtUseCase/GetListCustomerDebt/`):**
    *   Create `GetListCustomerDebtUseCaseTests.cs`.
    *   Follow `test_code.mdc`: Use Moq, AAA pattern, BDD-style naming (e.g., `ExecuteAsync_WhenCustomerExistsAndHasDebts_ShouldReturnCorrectlyPagedAndAggregatedDebts`, `ExecuteAsync_WhenStartDateIsProvided_ShouldFilterResultsCorrectly`, `ExecuteAsync_WhenValidationFails_ShouldReturnFailureResult` ).
    *   Mock all dependencies (`IValidator`, `IQueryService`, `IMapper`, `ITenantProvider`, `IMultiCurrencyLegacyService`, customer debt service/repository).
    *   Set up `IQueryService` mocks carefully to return appropriate data for the count query and the main data query based on input parameters.
    *   Verify interactions, especially the structure of the query passed to `IQueryService` and the parameters used.
    *   Cover scenarios: validation success/failure, customer found/not found, debts exist/do not exist, pagination works (skip/take), date filtering works, aggregation logic is correct, rounding is applied, correct `TotalDebt` is included.

Please generate all required files and code snippets, placing them in the correct locations within the `src/Modules/Customer/` directory structure. Add comments as per `comment_code.mdc`.