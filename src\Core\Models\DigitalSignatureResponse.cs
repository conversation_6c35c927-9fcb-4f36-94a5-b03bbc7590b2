using System.Text.Json.Serialization;

namespace KvFnB.Core.Models;

/// <summary>
/// Response model for digital signature status
/// </summary>
public class DigitalSignatureResponse
{
    /// <summary>
    /// Total number of free digital signatures allocated
    /// </summary>
    [JsonPropertyName("total_free")]
    public int TotalFree { get; init; }

    /// <summary>
    /// Number of free digital signatures remaining
    /// </summary>
    [JsonPropertyName("remain_free")]
    public int RemainFree { get; init; }
} 