﻿using KvFnB.Modules.Supplier.Domain.Models;
using Xunit;
using Assert = Xunit.Assert;

namespace KvFnB.Modules.Supplier.Domain.Tests.Models
{
    public class SupplierTests
    {
        [Fact]
        public void UpdateSupplierActive_ShouldSetIsActiveTrue()
        {
            var supplier = new Domain.Models.Supplier { IsActive = false };

            supplier.UpdateSupplierActive();

            Assert.True(supplier.IsActive);
        }

        [Fact]
        public void UpdateSupplierDeActive_ShouldSetIsActiveFalse()
        {
            var supplier = new Domain.Models.Supplier { IsActive = true };

            supplier.UpdateSupplierDeActive();

            Assert.False(supplier.IsActive);
        }

        [Fact]
        public void UpdateBasicInfo_ShouldUpdateAllFields_AndSetSearchNumber()
        {
            var supplier = new Domain.Models.Supplier();
            var phone = "0123-456 789";

            supplier.UpdateBasicInfo("Name", "Company", phone, "<EMAIL>", "ghi chú");

            Assert.Equal("Name", supplier.Name);
            Assert.Equal("Company", supplier.Company);
            Assert.Equal(phone, supplier.Phone);
            Assert.Equal("<EMAIL>", supplier.Email);
            Assert.Equal("ghi chú", supplier.Comment);
            Assert.Equal("0*********", supplier.SearchNumber);
        }


        [Fact]
        public void AssignGroup_ShouldSetGroupDetails()
        {
            var groups = new List<SupplierGroupDetail>
            {
                new SupplierGroupDetail(),
                new SupplierGroupDetail()
            };
            var supplier = new Domain.Models.Supplier();

            supplier.AssignGroup(groups);

            Assert.Equal(groups, supplier.SupplierGroupDetails);
        }

        [Fact]
        public void UpdateTaxCode_ShouldUpdateTaxCode()
        {
            var supplier = new Domain.Models.Supplier();

            supplier.UpdateTaxCode("*********");

            Assert.Equal("*********", supplier.TaxCode);
        }

        [Fact]
        public void UpdateCode_ShouldSetCode()
        {
            var supplier = new Domain.Models.Supplier();

            supplier.UpdateCode("NCC001");

            Assert.Equal("NCC001", supplier.Code);
        }

        [Fact]
        public void ChangeSupplierCode_ShouldSetCode()
        {
            var supplier = new Domain.Models.Supplier();

            supplier.ChangeSupplierCode("NCC002");

            Assert.Equal("NCC002", supplier.Code);
        }
    }
}
