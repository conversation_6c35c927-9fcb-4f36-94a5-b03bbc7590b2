using KvFnB.Core.Abstractions;
using KvFnB.Shared.MessageQueueProvider.Kafka;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace KvFnB.Shared.DependencyInjection
{
    /// <summary>
    /// Extension methods for setting up Kafka command dispatcher services in an <see cref="IServiceCollection" />.
    /// </summary>
    public static class KafkaServiceCollectionExtensions
    {
        /// <summary>
        /// Adds Kafka command dispatcher services to the specified <see cref="IServiceCollection" />.
        /// </summary>
        /// <param name="services">The <see cref="IServiceCollection" /> to add services to.</param>
        /// <param name="configuration">The configuration being bound.</param>
        /// <returns>The <see cref="IServiceCollection"/> so that additional calls can be chained.</returns>
        public static IServiceCollection AddKafkaCommandDispatcher(this IServiceCollection services, IConfiguration configuration)
        {
            // Bind Kafka configuration
            var kafkaConfig = new KafkaConfig();
            configuration.GetSection("Kafka").Bind(kafkaConfig);
            
            // Register KafkaConfig as singleton
            services.AddSingleton(kafkaConfig);
            
            // Register KafkaCommandDispatcher
            services.AddSingleton<ICommandDispatcher, KafkaCommandDispatcher>();
            
            return services;
        }
    }
} 