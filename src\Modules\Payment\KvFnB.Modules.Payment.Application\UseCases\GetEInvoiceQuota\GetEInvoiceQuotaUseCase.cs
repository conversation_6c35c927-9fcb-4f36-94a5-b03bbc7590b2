using System;
using System.Threading;
using System.Threading.Tasks;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;

namespace KvFnB.Modules.Payment.Application.UseCases.GetEInvoiceQuota;

public class GetEInvoiceQuotaUseCase : UseCaseBase<GetEInvoiceQuotaRequest, GetEInvoiceQuotaResponse>
{
    private readonly IValidator<GetEInvoiceQuotaRequest> _validator;
    private readonly IKmaService _kmaService;
    private readonly ILogger _logger;
    private readonly ITenantProvider _tenantProvider;

    public GetEInvoiceQuotaUseCase(
        IValidator<GetEInvoiceQuotaRequest> validator,
        IKmaService kmaService,
        ILogger logger,
        ITenantProvider tenantProvider)
    {
        _validator = validator ?? throw new ArgumentNullException(nameof(validator));
        _kmaService = kmaService ?? throw new ArgumentNullException(nameof(kmaService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
    }

    public override async Task<Result<GetEInvoiceQuotaResponse>> ExecuteAsync(
        GetEInvoiceQuotaRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate request
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Result<GetEInvoiceQuotaResponse>.Failure(validationResult.Errors);
            }

            // Get retailerId from tenant provider
            var retailerId = _tenantProvider.GetTenantId() ?? 0;

            // Call KMA service
            var quota = await _kmaService.GetEInvoiceQuotaAsync(
                request.TaxCode,
                retailerId,
                cancellationToken);

            var response = new GetEInvoiceQuotaResponse
            {
                TotalCount = quota.TotalCount,
                UsedCount = quota.UsedCount
            };

            return Result<GetEInvoiceQuotaResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.Error($"Error getting e-invoice quota: {ex.Message}", ex);
            return Result<GetEInvoiceQuotaResponse>.Failure(
                "Failed to get e-invoice quota");
        }
    }
} 