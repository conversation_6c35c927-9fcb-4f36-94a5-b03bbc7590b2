using System.ComponentModel;

namespace KvFnB.Core.Abstractions
{
    /// <summary>
    /// Generic factory interface for creating use cases
    /// </summary>
    [Description("Generic factory interface for creating use cases with lazy loading support.")]
    public interface IUseCaseFactory 
    {
        /// <summary>
        /// Gets the appropriate use case for the specified key
        /// </summary>
        /// <typeparam name="TUseCase">The type of use case to retrieve</typeparam>
        /// <param name="useCase">The use case to retrieve</param>
        /// <exception cref="ArgumentException">Thrown when no use case is found for the specified key</exception>
        [Description("Retrieves a use case instance by key with lazy initialization.")]
        TUseCase GetUseCase<TUseCase>();
    }
} 