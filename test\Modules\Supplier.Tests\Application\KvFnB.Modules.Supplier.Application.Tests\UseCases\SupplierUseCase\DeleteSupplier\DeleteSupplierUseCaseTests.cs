﻿using KvFnB.Core.Validation;
using KvFnB.Core.Abstractions;
using KvFnB.Modules.Supplier.Domain.Repositories;
using Moq;
using Xunit;
using Assert = Xunit.Assert;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeleteSupplier;
using Dapper;

namespace KvFnB.Modules.Supplier.Application.Tests.UseCases.SupplierUseCase.DeleteSupplier
{
    public class DeleteSupplierUseCaseTests
    {
        private readonly Mock<IValidator<DeleteSupplierRequest>> _validatorMock;
        private readonly Mock<IQueryService> _queryServiceMock;
        private readonly Mock<ISupplierRepository> _supplierRepositoryMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<ILogger> _loggerMock;

        private readonly DeleteSupplierUseCase _useCase;

        public DeleteSupplierUseCaseTests()
        {
            _validatorMock = new Mock<IValidator<DeleteSupplierRequest>>();
            _queryServiceMock = new Mock<IQueryService>();
            _supplierRepositoryMock = new Mock<ISupplierRepository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger>();

            _useCase = new DeleteSupplierUseCase(
                _validatorMock.Object,
                _queryServiceMock.Object,
                _supplierRepositoryMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object
            );

            _validatorMock.Setup(v => v.Validate(It.IsAny<DeleteSupplierRequest>()))
                .Returns(ValidationResult.Success());
        }

        [Fact]
        public async Task ExecuteAsync_ShouldReturnValidationFailure_WhenInvalidRequest()
        {
            var request = new DeleteSupplierRequest { SupplierId = 0 };
            var validationResult = ValidationResult.Failure(new List<string> { "SupplierId must be greater than 0" });

            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            var result = await _useCase.ExecuteAsync(request);

            Assert.False(result.IsSuccess);
            Assert.Equal(validationResult.Errors, result.ValidationErrors);
        }

        [Fact]
        public async Task ExecuteAsync_ShouldReturnFailure_WhenSupplierNotFound()
        {
            var request = new DeleteSupplierRequest { SupplierId = 1 };

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<Domain.Models.Supplier>(
                    It.IsAny<string>(), It.IsAny<DynamicParameters>()))
                .ReturnsAsync(new List<Domain.Models.Supplier>());

            var result = await _useCase.ExecuteAsync(request);

            Assert.False(result.IsSuccess);
            Assert.Contains("không tồn tại", result.ErrorMessage);
        }

        [Fact]
        public async Task ExecuteAsync_ShouldReturnSuccess_WhenSupplierDeleted()
        {
            var request = new DeleteSupplierRequest { SupplierId = 1 };
            var supplier = new Domain.Models.Supplier
            {
                Id = 1,
                Code = "ABC",
                Name = "Test",
                Phone = "123",
                IsDeleted = false
            };

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<Domain.Models.Supplier>(
                    It.IsAny<string>(), It.IsAny<DynamicParameters>()))
                .ReturnsAsync(new List<Domain.Models.Supplier> { supplier });

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<string>(
                    It.IsAny<string>(), It.IsAny<DynamicParameters>()))
                .ReturnsAsync(new List<string>());

            _supplierRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Domain.Models.Supplier>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(supplier);

            _unitOfWorkMock.Setup(u => u.CommitAsync(It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            var result = await _useCase.ExecuteAsync(request);

            Assert.True(result.IsSuccess);
            Assert.Equal(supplier.Id, result.Value!.Id);
            Assert.True(result.Value.IsDeleted);
        }


        [Fact]
        public async Task ExecuteAsync_ShouldReturnFailure_WhenExceptionOccurs()
        {
            var request = new DeleteSupplierRequest { SupplierId = 1 };

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<Domain.Models.Supplier>(
                    It.IsAny<string>(), It.IsAny<DynamicParameters>()))
                .ThrowsAsync(new Exception("DB error"));

            var result = await _useCase.ExecuteAsync(request);

            Assert.False(result.IsSuccess);
            Assert.Contains("Internal", result.ErrorMessage);
        }
    }
}
