using KvFnB.Shared.DistributedLock;
using Moq;
using StackExchange.Redis;

namespace KvFnB.Shared.Tests.DistributedLock
{
    public class RedisDistributedLockProviderTests
    {
        private readonly RedisDistributedLockProvider _lockProvider;
        private readonly Mock<IConnectionMultiplexer> _mockConnectionMultiplexer;
        private readonly Mock<IDatabase> _mockDatabase;

        public RedisDistributedLockProviderTests()
        {
            _mockConnectionMultiplexer = new Mock<IConnectionMultiplexer>();
            _mockDatabase = new Mock<IDatabase>();
            
            // Setup the connection multiplexer to return our mock database
            _mockConnectionMultiplexer.Setup(m => m.GetDatabase(It.IsAny<int>(), It.IsAny<object>()))
                .Returns(_mockDatabase.Object);
                
            _lockProvider = new RedisDistributedLockProvider(_mockConnectionMultiplexer.Object);
        }

        [Fact]
        public void Constructor_WithNullConnectionMultiplexer_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new RedisDistributedLockProvider(null!));
        }

        [Fact]
        public async Task LockAsync_WithAction_AcquiresLockAndExecutesAction()
        {
            // Arrange
            string lockKey = Guid.NewGuid().ToString();
            var timeout = TimeSpan.FromSeconds(10);
            var actionExecuted = false;
            Func<Task> action = async () => { actionExecuted = true; await Task.CompletedTask; };   

            // Setup database to return true for lock acquisition
            _mockDatabase.Setup(db => db.StringSetAsync(
                It.Is<RedisKey>(key => key == $"lock:{lockKey}"),
                It.IsAny<RedisValue>(),
                It.IsAny<TimeSpan>(),
                When.NotExists))
                .ReturnsAsync(true);

            // Act
            await _lockProvider.LockAsync(lockKey, timeout, async () => { await action(); });

            // Assert
            Assert.True(actionExecuted);
            _mockDatabase.Verify(db => db.StringSetAsync(
                It.Is<RedisKey>(key => key == $"lock:{lockKey}"),
                It.IsAny<RedisValue>(),
                It.IsAny<TimeSpan>(),
                When.NotExists), Times.Once);
        }

        [Fact]
        public async Task LockAsync_WithAction_DoesNotExecuteWhenLockCannotBeAcquired()
        {
            // Arrange
            const string lockKey = "test-lock";
            var timeout = TimeSpan.FromSeconds(10);
            var actionExecuted = false;
            Func<Task> action = async () => { actionExecuted = true; await Task.CompletedTask; };

            // Setup database to return false for lock acquisition (lock already exists)
            _mockDatabase.Setup(db => db.StringSetAsync(
                It.Is<RedisKey>(key => key == $"lock:{lockKey}"),
                It.IsAny<RedisValue>(),
                It.IsAny<TimeSpan>(),
                When.NotExists))
                .ReturnsAsync(false);

            // Act
            await _lockProvider.LockAsync(lockKey, timeout, async () => { await action(); });
            
            // Assert
            Assert.False(actionExecuted);
        }

        [Fact]
        public async Task LockAsync_WithSingleParameterFunc_AcquiresLockAndExecutesFunction()
        {
            // Arrange
            const string lockKey = "test-lock";
            var timeout = TimeSpan.FromSeconds(10);
            int parameter = 5;
            int functionResult = 0;
            
            Func<int, Task<int>> func = async (p) => {
                functionResult = p * 2;
                return functionResult;
            };

            // Setup database to return true for lock acquisition
            _mockDatabase.Setup(db => db.StringSetAsync(
                It.Is<RedisKey>(key => key == $"lock:{lockKey}"),
                It.IsAny<RedisValue>(),
                It.IsAny<TimeSpan>(),
                When.NotExists))
                .ReturnsAsync(true);

            // Act
            await _lockProvider.LockAsync(lockKey, timeout, parameter, func);

            // Assert
            Assert.Equal(10, functionResult);
            _mockDatabase.Verify(db => db.StringSetAsync(
                It.Is<RedisKey>(key => key == $"lock:{lockKey}"),
                It.IsAny<RedisValue>(),
                It.IsAny<TimeSpan>(),
                When.NotExists), Times.Once);
        }

        [Fact]
        public async Task LockAsync_WithTwoParameterFunc_AcquiresLockAndExecutesFunction()
        {
            // Arrange
            const string lockKey = "test-lock";
            var timeout = TimeSpan.FromSeconds(10);
            int param1 = 5;
            string param2 = "test";
            string functionResult = string.Empty;
            
            Func<int, string, Task<string>> func = async (p1, p2) => {
                functionResult = $"{p2}-{p1}";
                return functionResult;
            };

            // Setup database to return true for lock acquisition
            _mockDatabase.Setup(db => db.StringSetAsync(
                It.Is<RedisKey>(key => key == $"lock:{lockKey}"),
                It.IsAny<RedisValue>(),
                It.IsAny<TimeSpan>(),
                When.NotExists))
                .ReturnsAsync(true);

            // Act
            await _lockProvider.LockAsync(lockKey, timeout, param1, param2, func);  

            // Assert
            Assert.Equal("test-5", functionResult);
            _mockDatabase.Verify(db => db.StringSetAsync(
                It.Is<RedisKey>(key => key == $"lock:{lockKey}"),
                It.IsAny<RedisValue>(),
                It.IsAny<TimeSpan>(),
                When.NotExists), Times.Once);
        }

        [Fact]
        public async Task LockAsync_WithSingleParameterFunc_DoesNotExecuteWhenLockCannotBeAcquired()
        {
            // Arrange
            const string lockKey = "test-lock";
            var timeout = TimeSpan.FromSeconds(10);
            int parameter = 5;
            bool funcExecuted = false;
            
            Func<int, Task<int>> func = (p) => {
                funcExecuted = true;
                return Task.FromResult(p * 2);
            };

            // Setup database to return false for lock acquisition
            _mockDatabase.Setup(db => db.StringSetAsync(
                It.Is<RedisKey>(key => key == $"lock:{lockKey}"),
                It.IsAny<RedisValue>(),
                It.IsAny<TimeSpan>(),
                When.NotExists))
                .ReturnsAsync(false);

            // Act
            await _lockProvider.LockAsync(lockKey, timeout, parameter, func);
            
            // Assert
            Assert.False(funcExecuted);
        }

        [Fact]
        public async Task LockAsync_WithTwoParameterFunc_DoesNotExecuteWhenLockCannotBeAcquired()
        {
            // Arrange
            const string lockKey = "test-lock";
            var timeout = TimeSpan.FromSeconds(10);
            int param1 = 5;
            string param2 = "test";
            bool funcExecuted = false;
            
            Func<int, string, Task<string>> func = (p1, p2) => {
                funcExecuted = true;
                return Task.FromResult($"{p2}-{p1}");
            };

            // Setup database to return false for lock acquisition
            _mockDatabase.Setup(db => db.StringSetAsync(
                It.Is<RedisKey>(key => key == $"lock:{lockKey}"),
                It.IsAny<RedisValue>(),
                It.IsAny<TimeSpan>(),
                When.NotExists))
                .ReturnsAsync(false);

            // Act
            await _lockProvider.LockAsync(lockKey, timeout, param1, param2, func);
            
            // Assert
            Assert.False(funcExecuted);
        }
    }
} 