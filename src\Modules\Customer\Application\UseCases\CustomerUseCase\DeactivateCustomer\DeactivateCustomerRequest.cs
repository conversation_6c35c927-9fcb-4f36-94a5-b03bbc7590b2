using System.ComponentModel;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.DeactivateCustomer
{
    /// <summary>
    /// Represents the request model for the DeactivateCustomer use case
    /// </summary>
    public record DeactivateCustomerRequest
    {
        /// <summary>
        /// The unique identifier of the customer to deactivate
        /// </summary>
        [JsonPropertyName("customer_id")]
        [Description("The unique identifier of the customer to deactivate")]
        public long CustomerId { get; init; }
    }
} 