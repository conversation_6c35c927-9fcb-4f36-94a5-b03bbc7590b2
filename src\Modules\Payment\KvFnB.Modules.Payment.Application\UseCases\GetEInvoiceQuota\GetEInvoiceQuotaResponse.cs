namespace KvFnB.Modules.Payment.Application.UseCases.GetEInvoiceQuota;

/// <summary>
/// Response model for e-invoice quota information
/// </summary>
public record GetEInvoiceQuotaResponse
{
    /// <summary>
    /// Total number of invoices allocated
    /// </summary>
    public int TotalCount { get; init; }

    /// <summary>
    /// Number of invoices already used
    /// </summary>
    public int UsedCount { get; init; }

    /// <summary>
    /// Number of invoices remaining
    /// </summary>
    public int RemainingCount => TotalCount - UsedCount;
} 