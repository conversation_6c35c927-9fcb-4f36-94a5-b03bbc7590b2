﻿using System.ComponentModel;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.ActiveSupplier
{
    /// <summary>
    /// Represents the request model for activating a supplier
    /// </summary>
    public record ActiveSupplierResponse
    {
        /// <summary>
        /// The ID of the activated supplier
        /// </summary>
        [JsonPropertyName("id"), Description("The ID of the activated supplier.")]
        public int Id { get; init; }

        /// <summary>
        /// The name of the activated supplier
        /// </summary>
        [JsonPropertyName("name"), Description("The name of the activated supplier.")]
        public string Name { get; init; } = string.Empty;

        /// <summary>
        /// Indicates whether the supplier is active
        /// </summary>
        [JsonPropertyName("is_active"), Description("Indicates whether the supplier is active.")]
        public bool IsActive { get; init; }
    }
}
