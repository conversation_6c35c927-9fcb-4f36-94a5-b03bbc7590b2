﻿using System.ComponentModel;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeActiveSupplier
{
    public class DeActiveSupplierResponse
    {
        [JsonPropertyName("id"), Description("The ID of the deactivated supplier.")]
        public int Id { get; init; }

        /// <summary>
        /// The name of the deactivated supplier
        /// </summary>
        [JsonPropertyName("name"), Description("The name of the deactivated supplier.")]
        public string Name { get; init; } = string.Empty;

        /// <summary>
        /// Indicates whether the supplier is deactive
        /// </summary>
        [JsonPropertyName("is_active"), Description("Indicates whether the supplier is deactive.")]
        public bool IsActive { get; init; }
    }
}
