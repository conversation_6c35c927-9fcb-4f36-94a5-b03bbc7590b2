using KvFnB.Core.Validation;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.CreateCustomer;
using Xunit;
using KvFnB.Modules.Customer.Application.Dtos;

namespace KvFnB.Modules.Customer.Tests.Application.UseCases.CustomerUseCase.CreateCustomer;

public class CreateCustomerValidatorTests
{
    private readonly CreateCustomerValidator _validator;

    public CreateCustomerValidatorTests()
    {
        _validator = new CreateCustomerValidator();
    }

    [Fact]
    public void Validate_WhenRequestIsValid_ShouldReturnSuccess()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Code = "CUST123",
            Name = "Test Customer",
            Email = "<EMAIL>",
            Type = 1,
            IsActive = true
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.Errors);
    }

    [Theory]
    [InlineData("", "Customer name is required.")]
    [InlineData("A very long name that is way over the allowed character limit for testing purposes. This is a really long name that would definitely exceed the maximum character limit imposed by the validator to ensure data integrity in the database and proper display in the UI.", "Customer name must be less than 255 characters.")]
    public void Validate_WhenNameIsInvalid_ShouldReturnFailure(string name, string expectedError)
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Code = "CUST123",
            Name = name,
            Type = 1
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains(expectedError, result.Errors);
    }

    [Theory]
    [InlineData("invalid-email", "Invalid email address format.")]
    [InlineData("missing-domain@", "Invalid email address format.")]
    [InlineData("@missing-local-part.com", "Invalid email address format.")]
    public void Validate_WhenEmailIsInvalid_ShouldReturnFailure(string email, string expectedError)
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Code = "CUST123",
            Name = "Test Customer",
            Email = email,
            Type = 1
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains(expectedError, result.Errors);
    }

    [Fact]
    public void Validate_WhenEmailIsVeryLong_ShouldReturnFailure()
    {
        // Arrange
        string longEmail = $"{new string('a', 245)}@example.com"; // Email > 255 chars
        var request = new CreateCustomerRequest
        {
            Code = "CUST123",
            Name = "Test Customer",
            Email = longEmail,
            Type = 1
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("Email must be less than 255 characters", result.Errors);
    }

    [Theory]
    [InlineData(0, "Customer type must be either 1 (Individual) or 2 (Corporate).")]
    [InlineData(3, "Customer type must be either 1 (Individual) or 2 (Corporate).")]
    public void Validate_WhenTypeIsInvalid_ShouldReturnFailure(byte type, string expectedError)
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Code = "CUST123",
            Name = "Test Customer",
            Type = type
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains(expectedError, result.Errors);
    }

    [Fact]
    public void Validate_WhenCorporateCustomerHasNoOrganization_ShouldPass()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Code = "CUST123",
            Name = "Test Customer",
            Type = 2, // Corporate
            Organization = null
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.Errors);
    }

    [Fact]
    public void Validate_WhenCodeTooLong_ShouldReturnFailure()
    {
        // Arrange
        var longCode = new string('a', 51);
        var request = new CreateCustomerRequest
        {
            Code = longCode,
            Name = "Test Customer",
            Type = 1
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("Customer code must be less than 50 characters.", result.Errors);
    }

    [Fact]
    public void Validate_WhenContactNumberTooLong_ShouldReturnFailure()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 1,
            ContactNumber = new string('1', 21) // 21 characters
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("Contact number must be less than 20 characters.", result.Errors);
    }

    [Fact]
    public void Validate_WhenContactNumberIsValid_ShouldPass()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 1,
            ContactNumber = "+1234567890" // Valid contact number
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.True(result.IsValid);
    }

    [Fact]
    public void Validate_WhenAddressTooLong_ShouldReturnFailure()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 1,
            Address = new string('A', 501) // 501 characters
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("Address must be less than 500 characters.", result.Errors);
    }

    [Fact]
    public void Validate_WhenCommentsTooLong_ShouldReturnFailure()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 1,
            Comments = new string('A', 1001) // 1001 characters
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("Comments must be less than 1000 characters.", result.Errors);
    }

    [Fact]
    public void Validate_WhenTaxCodeTooLong_ShouldReturnFailure()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 1,
            TaxCode = new string('1', 21) // 21 characters
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("Tax code must be less than 20 characters.", result.Errors);
    }

    #region Organization Tests

    [Fact]
    public void Validate_WhenTypeIsIndividualAndOrganizationIsEmpty_ShouldPass()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 1, // Individual
            Organization = string.Empty
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.True(result.IsValid);
    }

    [Fact]
    public void Validate_WhenTypeIsIndividualAndOrganizationIsNull_ShouldPass()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 1, // Individual
            Organization = null
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.True(result.IsValid);
    }

    [Fact]
    public void Validate_WhenTypeIsCorporateAndOrganizationIsProvided_ShouldPass()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 2, // Corporate
            Organization = "Test Organization"
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.True(result.IsValid);
    }

    [Fact]
    public void Validate_WhenTypeIsCorporateAndOrganizationIsEmpty_ShouldPass()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 2, // Corporate
            Organization = string.Empty
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.True(result.IsValid);
    }


    [Fact]
    public void Validate_WhenOrganizationExceeds255Characters_ShouldFail()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 2, // Corporate
            Organization = new string('A', 256) // 256 characters
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.Contains("Organization must be less than 255 characters"));
    }

    [Fact]
    public void Validate_WhenOrganizationIs255Characters_ShouldPass()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 2, // Corporate
            Organization = new string('A', 255) // 255 characters
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.True(result.IsValid);
    }

    #endregion

    #region AddressLine Tests

    [Fact]
    public void Validate_WhenAddressLocationIsNull_ShouldPass()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 1,
            AddressLocation = null
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.True(result.IsValid);
    }

    [Fact]
    public void Validate_WhenAddressLocationAddressLineIsNull_ShouldPass()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 1,
            AddressLocation = new AddressRequest
            {
                AdministrativeAreaId = 1
            }
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.True(result.IsValid);
    }

    [Fact]
    public void Validate_WhenAddressLocationAddressLineIsEmpty_ShouldPass()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 1,
            AddressLocation = new AddressRequest
            {
                AddressLine = string.Empty,
                AdministrativeAreaId = 1
            }
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.True(result.IsValid);
    }

    [Fact]
    public void Validate_WhenAddressLocationAddressLineIs255Characters_ShouldPass()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 1,
            AddressLocation = new AddressRequest
            {
                AddressLine = new string('A', 255), // 255 characters
                AdministrativeAreaId = 1
            }
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.True(result.IsValid);
    }

    [Fact]
    public void Validate_WhenAddressLocationAddressLineExceeds255Characters_ShouldFail()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 1,
            AddressLocation = new AddressRequest
            {
                AddressLine = new string('A', 256), // 256 characters
                AdministrativeAreaId = 1
            }
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.Contains("Address must be less than 255 characters"));
    }

    [Fact]
    public void Validate_WhenAddressLocationAddressLineIsValidWithSpecialCharacters_ShouldPass()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 1,
            AddressLocation = new AddressRequest
            {
                AddressLine = "123 Main St, Apt #456, Building 7, Floor 8",
                AdministrativeAreaId = 1
            }
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.True(result.IsValid);
    }

    [Fact]
    public void Validate_WhenAddressLocationAddressLineHasUnicodeCharacters_ShouldPass()
    {
        // Arrange
        var request = new CreateCustomerRequest
        {
            Name = "Test Customer",
            Type = 1,
            AddressLocation = new AddressRequest
            {
                AddressLine = "123 Cầu Giấy, Hà Nội, Việt Nam",
                AdministrativeAreaId = 1
            }
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.True(result.IsValid);
    }

    #endregion
} 