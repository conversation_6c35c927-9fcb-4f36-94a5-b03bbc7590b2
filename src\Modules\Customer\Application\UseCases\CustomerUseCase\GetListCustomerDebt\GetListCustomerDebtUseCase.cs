using Dapper;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.Customer.Application.Dtos;
using KvFnB.Modules.Customer.Domain.Enums;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetListCustomerDebt
{
    /// <summary>
    /// Use case for retrieving a list of customer debt records
    /// </summary>
    public class GetListCustomerDebtUseCase : UseCaseBase<GetListCustomerDebtRequest, GetListCustomerDebtResponse>
    {
        private readonly IQueryService _queryService;
        private readonly ITenantProvider _tenantProvider;
        private readonly IValidator<GetListCustomerDebtRequest> _validator;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetListCustomerDebtUseCase"/> class.
        /// </summary>
        /// <param name="queryService">The query service</param>
        /// <param name="tenantProvider">The tenant provider</param>
        /// <param name="validator">The validator</param>
        /// <exception cref="ArgumentNullException">Thrown if any dependencies are null</exception>
        public GetListCustomerDebtUseCase(
            IQueryService queryService,
            ITenantProvider tenantProvider,
            IValidator<GetListCustomerDebtRequest> validator)
        {
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
        }

        /// <summary>
        /// Executes the use case to retrieve customer debt records
        /// </summary>
        /// <param name="request">The request containing filter parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A result containing the list of customer debts</returns>
        public override async Task<Result<GetListCustomerDebtResponse>> ExecuteAsync(
            GetListCustomerDebtRequest request,
            CancellationToken cancellationToken = default)
        {
            // Validate the request
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Result<GetListCustomerDebtResponse>.Failure(validationResult.Errors);
            }

            try
            {
                // Get tenant ID (retailer ID)
                var retailerId = _tenantProvider.GetTenantId() ?? 0;
                var isExistedCustomer = await IsExistedCustomer(retailerId, request.CustomerId);
                if (!isExistedCustomer)
                {
                    return Result<GetListCustomerDebtResponse>.Failure("Customer not found");
                }
                
                // Create parameters for query
                var parameters = new DynamicParameters(new Dictionary<string, object>
                {
                    { "@retailerId", retailerId },
                    { "@customerId", request.CustomerId },
                    { "@skip", request.Skip },
                    { "@take", request.Take },
                    { "@dataZone", BalanceTrackTypes.Customer } // Customer DataZone
                });

                // Build date range filter if provided
                var dateFilter = string.Empty;
                if (request.StartDate.HasValue)
                {
                    parameters.Add("@startDate", request.StartDate.Value);
                    dateFilter += " AND bt.TransDate >= @startDate";
                }
                
                if (request.EndDate.HasValue)
                {
                    parameters.Add("@endDate", request.EndDate.Value);
                    dateFilter += " AND bt.TransDate <= @endDate";
                }
                
                // SQL to get document codes for pagination
                var documentCodesQuery = $@"
                    WITH DocumentCodes AS (
                        SELECT 
                            bt.DocumentCode,
                            MAX(bt.TransDate) AS LastTransDate
                        FROM BalanceTracking bt WITH(NOLOCK)
                        WHERE 
                            bt.RetailerId = @retailerId
                            AND bt.PartnerId = @customerId
                            AND bt.DataZone = @dataZone
                            AND bt.Value != 0
                            {dateFilter}
                        GROUP BY bt.DocumentCode
                    )
                    SELECT DocumentCode
                    FROM DocumentCodes
                    ORDER BY LastTransDate DESC
                    OFFSET @skip ROWS
                    FETCH NEXT @take ROWS ONLY";
                
                // SQL to count total documents
                var countQuery = $@"
                    SELECT COUNT(DISTINCT DocumentCode) AS TotalItems
                    FROM BalanceTracking WITH(NOLOCK)
                    WHERE 
                        RetailerId = @retailerId
                        AND PartnerId = @customerId
                        AND DataZone = @dataZone
                        AND Value != 0
                        {dateFilter.Replace("bt.", "")}";
                
                // SQL to get total debt
                var totalDebtQuery = $@"
                    WITH LatestBalances AS (
                        SELECT Top(1)
                            DocumentCode,
                            Balance,
                            ROW_NUMBER() OVER (PARTITION BY DocumentCode ORDER BY TransDate DESC) AS RowNum
                        FROM BalanceTracking WITH(NOLOCK)
                        WHERE 
                            RetailerId = @retailerId
                            AND PartnerId = @customerId
                            AND DataZone = @dataZone
                            AND Value != 0
                            {dateFilter.Replace("bt.", "")}
                            Order by TransDate DESC
                    )
                    SELECT ISNULL(SUM(Balance), 0) AS TotalDebt
                    FROM LatestBalances
                    WHERE RowNum = 1";

                // Execute queries
                var documentCodes = await _queryService.QueryPlainTextAsync<string>(documentCodesQuery, parameters);
                var totalItems = (await _queryService.QueryPlainTextAsync<long>(countQuery, parameters)).FirstOrDefault();
                var totalDebt = (await _queryService.QueryPlainTextAsync<decimal>(totalDebtQuery, parameters)).FirstOrDefault();
                
                if (!documentCodes.Any())
                {
                    return Result<GetListCustomerDebtResponse>.Success(new GetListCustomerDebtResponse
                    {
                        TotalItems = totalItems,
                        TotalDebt = totalDebt,
                        Items = new List<CustomerDebtDto>()
                    });
                }

                // Get detailed debt information for found document codes
                var detailQuery = $@"
                    SELECT
                        bt.DocumentCode,
                        bt.DocumentType,
                        bt.DocumentId,
                        bt.TransDate,
                        bt.Balance,
                        bt.Value
                    FROM BalanceTracking bt WITH(NOLOCK)
                    WHERE 
                        bt.RetailerId = @retailerId
                        AND bt.PartnerId = @customerId
                        AND bt.DataZone = @dataZone
                        AND bt.DocumentCode IN @documentCodes
                    ORDER BY bt.TransDate DESC";
                
                parameters.Add("@documentCodes", documentCodes.ToList());
                var debtDetails = await _queryService.QueryPlainTextAsync<CustomerDebtDto>(detailQuery, parameters);
                
                // Process and group data
                var groupedDebts = debtDetails
                    .GroupBy(d => d.DocumentCode)
                    .Select(group => new CustomerDebtDto
                    {
                        DocumentCode = group.Key,
                        DocumentType = group.FirstOrDefault()?.DocumentType ?? PaymentDocTypes.Invoice,
                        DocumentId = group.OrderByDescending(d => d.DocumentId).FirstOrDefault()?.DocumentId ?? 0,
                        TransDate = group.FirstOrDefault()?.TransDate ?? default,
                        Balance = group.OrderByDescending(d => d.TransDate).ThenByDescending(d => d.DocumentId).FirstOrDefault()?.Balance ?? 0,
                        Value = group.Sum(d => d.Value)
                    })
                    .OrderByDescending(d => d.TransDate)
                    .ToList();
                
                // Build response
                var response = new GetListCustomerDebtResponse
                {
                    TotalItems = totalItems,
                    TotalDebt = totalDebt,
                    Items = groupedDebts
                };
                
                return Result<GetListCustomerDebtResponse>.Success(response);
            }
            catch (Exception ex)
            {
                // Log the exception (implement logging if needed)
                return Result<GetListCustomerDebtResponse>.Failure("Internal server error");
            }
        }

        private async Task<bool> IsExistedCustomer(long retailerId, long customerId)
        {
            var query = $@"SELECT COUNT(1) FROM Customer WITH(NOLOCK) WHERE RetailerId = @retailerId AND Id = @customerId";
            var parameters = new DynamicParameters();
            parameters.Add("retailerId", retailerId);
            parameters.Add("customerId", customerId);

            return (await _queryService.QueryPlainTextAsync<bool>(query, parameters)).FirstOrDefault();
        }
    }
} 