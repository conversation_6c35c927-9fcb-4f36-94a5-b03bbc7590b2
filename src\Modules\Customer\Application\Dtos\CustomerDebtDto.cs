using System;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Customer.Application.Dtos
{
    /// <summary>
    /// Represents a single customer debt entry
    /// </summary>
    public record CustomerDebtDto
    {
        /// <summary>
        /// The unique document code of the debt
        /// </summary>
        [JsonPropertyName("document_code"), Description("The unique document code of the debt.")]
        public string DocumentCode { get; init; } = string.Empty;

        /// <summary>
        /// The type of payment document
        /// </summary>
        [JsonPropertyName("document_type"), Description("The type of payment document.")]
        public PaymentDocTypes DocumentType { get; init; }

        /// <summary>
        /// The unique identifier of the document
        /// </summary>
        [JsonPropertyName("document_id"), Description("The unique identifier of the document.")]
        public long DocumentId { get; init; }

        /// <summary>
        /// The date of the transaction
        /// </summary>
        [JsonPropertyName("trans_date"), Description("The date of the transaction.")]
        public DateTime TransDate { get; init; }

        /// <summary>
        /// The current balance of the debt
        /// </summary>
        [JsonPropertyName("balance"), Description("The current balance of the debt.")]
        public decimal Balance { get; init; }

        /// <summary>
        /// The original value of the debt
        /// </summary>
        [JsonPropertyName("value"), Description("The original value of the debt.")]
        public decimal Value { get; init; }
    }
    
    /// <summary>
    /// Enumeration of payment document types
    /// </summary>
    public enum PaymentDocTypes
    {
        /// <summary>
        /// Invoice payment document
        /// </summary>
        Invoice = 0,
        
        /// <summary>
        /// Receipt payment document
        /// </summary>
        Receipt = 1,
        
        /// <summary>
        /// Return payment document
        /// </summary>
        Return = 2,
        
        /// <summary>
        /// Payment payment document
        /// </summary>
        Payment = 3
    }
} 