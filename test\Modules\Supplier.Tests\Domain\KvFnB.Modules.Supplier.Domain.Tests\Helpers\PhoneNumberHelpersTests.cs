﻿using KvFnB.Modules.Supplier.Domain.Helpers;
using Xunit;
using Assert = Xunit.Assert;

namespace KvFnB.Modules.Supplier.Domain.Tests.Helpers
{
    public class PhoneNumberHelpersTests
    {
        [Theory]
        [InlineData(null, "")]
        [InlineData("", "")]
        [InlineData("   ", "")]
        [InlineData("0123-456 789", "0123456789")]
        [InlineData("  0123 456-789  ", "0123456789")]
        [InlineData("0123-456-789", "0123456789")]
        [InlineData("   0123456789   ", "0123456789")]
        public void GetPerfectContactNumber_ShouldCleanInput(string input, string expected)
        {
            // Act
            var result = PhoneNumberHelpers.GetPerfectContactNumber(input);

            // Assert
            Assert.Equal(expected, result);
        }
    }
}
