﻿using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeleteSupplier;
using Xunit;
using Assert = Xunit.Assert;

namespace KvFnB.Modules.Supplier.Application.Tests.UseCases.SupplierUseCase.DeleteSupplier
{
    public class DeleteSupplierValidatorTests
    {
        private readonly DeleteSupplierValidator _validator;

        public DeleteSupplierValidatorTests()
        {
            _validator = new DeleteSupplierValidator();
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        public void Validate_ShouldReturnError_WhenSupplierIdIsInvalid(int supplierId)
        {
            // Arrange
            var request = new DeleteSupplierRequest { SupplierId = supplierId };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("Supplier ID must be greater than zero.", result.Errors);
        }

        [Fact]
        public void Validate_ShouldPass_WhenSupplierIdIsValid()
        {
            // Arrange
            var request = new DeleteSupplierRequest { SupplierId = 123 };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }
    }
}
