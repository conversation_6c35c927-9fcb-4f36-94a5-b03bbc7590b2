using System;
using System.Text.Json.Serialization;
using KvFnB.Core.Domain.Commands;

namespace KvFnB.Shared.MessageQueueProvider.Kafka.Models
{
    /// <summary>
    /// Data transfer object for serializing digital commands to <PERSON>f<PERSON>.
    /// </summary>
    internal class DigitalCommandDto
    {
        /// <summary>
        /// The unique event identifier.
        /// </summary>
        [JsonPropertyName("eventId")]
        public Guid EventId { get; set; }
        
        /// <summary>
        /// The type of the event.
        /// </summary>
        [JsonPropertyName("eventType")]
        public string EventType { get; set; }
        
        /// <summary>
        /// The retailer code.
        /// </summary>
        [JsonPropertyName("retailerCode")]
        public string RetailerCode { get; set; }
        
        /// <summary>
        /// The retailer identifier.
        /// </summary>
        [JsonPropertyName("retailerId")]
        public int RetailerId { get; set; }
        
        /// <summary>
        /// The branch identifier.
        /// </summary>
        [JsonPropertyName("branchId")]
        public long BranchId { get; set; }
        
        /// <summary>
        /// The group identifier.
        /// </summary>
        [JsonPropertyName("groupId")]
        public int GroupId { get; set; }
        
        /// <summary>
        /// The user identifier.
        /// </summary>
        [JsonPropertyName("userId")]
        public long UserId { get; set; }
        
        /// <summary>
        /// The command issuer identifier.
        /// </summary>
        [JsonPropertyName("commandIssuer")]
        public string CommandIssuer { get; set; }
        
        /// <summary>
        /// The serialized command data.
        /// </summary>
        [JsonPropertyName("commandData")]
        public string CommandData { get; set; }

        /// <summary>
        /// Default constructor for serialization purposes.
        /// </summary>
        public DigitalCommandDto()
        {
        }

        /// <summary>
        /// Creates a new instance of DigitalCommandDto from a BaseDigitalCommand.
        /// </summary>
        /// <param name="command">The BaseDigitalCommand to serialize.</param>
        /// <param name="commandData">The serialized command data.</param>
        public DigitalCommandDto(BaseDigitalCommand command, string commandData)
        {
            EventId = command.EventId;
            EventType = command.EventType;
            RetailerCode = command.RetailerCode;
            RetailerId = command.RetailerId;
            BranchId = command.BranchId;
            GroupId = command.GroupId;
            UserId = command.UserId;
            CommandIssuer = command.CommandIssuer;
            CommandData = commandData;
        }
        
        /// <summary>
        /// Creates a new instance of DigitalCommandDto from a BaseDigitalCommand using default serialization.
        /// </summary>
        /// <param name="command">The BaseDigitalCommand to serialize.</param>
        public DigitalCommandDto(BaseDigitalCommand command)
            : this(command, System.Text.Json.JsonSerializer.Serialize(command))
        {
        }
    }
} 