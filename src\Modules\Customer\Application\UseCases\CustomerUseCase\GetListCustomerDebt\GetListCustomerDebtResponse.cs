using System.Collections.Generic;
using System.ComponentModel;
using System.Text.Json.Serialization;
using KvFnB.Modules.Customer.Application.Dtos;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetListCustomerDebt
{
    /// <summary>
    /// Response model for customer debt listing
    /// </summary>
    public record GetListCustomerDebtResponse
    {
        /// <summary>
        /// Total number of debt items found (excluding pagination)
        /// </summary>
        [JsonPropertyName("total_items"), Description("Total number of debt items found.")]
        public long TotalItems { get; init; }
        
        /// <summary>
        /// Collection of customer debt items
        /// </summary>
        [JsonPropertyName("items"), Description("Collection of customer debt items.")]
        public IReadOnlyList<CustomerDebtDto> Items { get; init; } = new List<CustomerDebtDto>();
        
        /// <summary>
        /// Total debt amount for the customer
        /// </summary>
        [JsonPropertyName("total_debt"), Description("Total debt amount for the customer.")]
        public decimal TotalDebt { get; init; }
    }
} 