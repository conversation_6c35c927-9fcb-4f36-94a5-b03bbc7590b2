﻿using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;

namespace KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeActiveSupplier
{
    public class DeActiveSupplierValidator:Validator<DeActiveSupplierRequest>
    {
        public DeActiveSupplierValidator()
        {
            RuleFor(x => x.SupplierId)
                .GreaterThan(0, "Supplier ID must be greater than zero.");
        }
    }
}
