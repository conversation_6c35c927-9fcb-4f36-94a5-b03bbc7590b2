namespace KvFnB.Shared.MessageQueueProvider.Kafka
{
    /// <summary>
    /// Configuration settings for the Kafka Command Dispatcher.
    /// </summary>
    public class KafkaConfig
    {
        /// <summary>
        /// Comma-separated list of Kafka broker addresses (e.g., "kafka1:9092,kafka2:9092").
        /// </summary>
        public string BootstrapServers { get; set; }
        
        /// <summary>
        /// Default topic for messages when no specific topic is provided.
        /// </summary>
        public string DefaultTopic { get; set; }
        
        /// <summary>
        /// Number of retry attempts for failed messages.
        /// </summary>
        public int RetryAttempts { get; set; } = 3;
        
        /// <summary>
        /// Client identifier for the Kafka producer.
        /// </summary>
        public string ClientId { get; set; }
        
        /// <summary>
        /// Maximum message size in bytes (default: 100MB).
        /// </summary>
        public int MessageMaxBytes { get; set; } = 104857600;
    }
} 