﻿using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;

namespace KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeleteSupplier
{
    public class DeleteSupplierValidator : Validator<DeleteSupplierRequest>
    {
        public DeleteSupplierValidator()
        {
            RuleFor(x => x.SupplierId)
                .GreaterThan(0, "Supplier ID must be greater than zero.");
        }
    }
}
