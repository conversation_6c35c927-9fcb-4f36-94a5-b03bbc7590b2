using KvFnB.Core.Abstractions;
using KvFnB.Modules.Payment.Application.UseCases.CheckDigitalSignatureStatus;
using KvFnB.Modules.Payment.Application.UseCases.GetEInvoiceQuota;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace KvFnB.Modules.Payment.Restful;

[ApiController]
[Route("e-invoice")]
[Authorize]
public class EInvoiceController : BaseApi
{
    private readonly CheckDigitalSignatureUseCase _checkSignatureUseCase;
    private readonly GetEInvoiceQuotaUseCase _getQuotaUseCase;
    private readonly IMapper _mapper;

    public EInvoiceController(
        IHttpContextAccessor httpContextAccessor,
        CheckDigitalSignatureUseCase checkSignatureUseCase,
        GetEInvoiceQuotaUseCase getQuotaUseCase,
        IMapper mapper) : base(httpContextAccessor)
    {
        _checkSignatureUseCase = checkSignatureUseCase ?? throw new ArgumentNullException(nameof(checkSignatureUseCase));
        _getQuotaUseCase = getQuotaUseCase ?? throw new ArgumentNullException(nameof(getQuotaUseCase));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }

    [HttpGet("digital-signature-status")]
    [SwaggerOperation(Summary = "Check digital signature status", Description = "Checks the digital signature status for a merchant")]
    [SwaggerResponse(StatusCodes.Status200OK, "Returns the digital signature status", typeof(CheckDigitalSignatureResponse))]
    [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid request or validation failed")]
    public async Task<IActionResult> CheckDigitalSignatureStatus()
    {
        var request = new CheckDigitalSignatureRequest();
        var result = await _checkSignatureUseCase.ExecuteAsync(request);
        return result.IsSuccess ? Success(result) : Failure(result);
    }

    [HttpGet("quota")]
    [SwaggerOperation(Summary = "Get e-invoice quota", Description = "Gets the e-invoice quota for a merchant")]
    [SwaggerResponse(StatusCodes.Status200OK, "Returns the e-invoice quota information", typeof(GetEInvoiceQuotaResponse))]
    [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid request or validation failed")]
    public async Task<IActionResult> GetEInvoiceQuota([FromQuery] string taxCode)
    {
        var request = new GetEInvoiceQuotaRequest
        {
            TaxCode = taxCode
        };

        var result = await _getQuotaUseCase.ExecuteAsync(request);
        return result.IsSuccess ? Success(result) : Failure(result);
    }
} 