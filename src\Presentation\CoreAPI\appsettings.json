{"ConnectionStrings": {"KiotVietFnBMaster": "Server=dc2d-fnb-mssql-01.citigo.io;Database=PhoenixKiotVietMaster;User Id=sa;Password=***************;MultipleActiveResultSets=True;TrustServerCertificate=True;", "KiotVietFnBShard": "Server=dc2d-fnb-mssql-01.citigo.io;Database=PhoenixKiotVietShard1;User Id=sa;Password=***************;MultipleActiveResultSets=True;TrustServerCertificate=True;", "KiotVietFnBShard1": "Server=dc2d-fnb-mssql-01.citigo.io;Database=PhoenixKiotVietShard1;User Id=sa;Password=***************;MultipleActiveResultSets=True;TrustServerCertificate=True;"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "log//log.json", "rollingInterval": "Day", "fileSizeLimitBytes": 10000000, "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Json.Json<PERSON>, Ser<PERSON>g"}}, {"Name": "<PERSON><PERSON><PERSON>"}], "Properties": {"ApplicationName": "KiotVietFnB.SyncGateway"}}, "RedisMq": {"Servers": "dc2d-fnb-infra-01.citigo.io:26380;dc2d-fnb-infra-02.citigo.io:26380;dc2d-fnb-infra-03.citigo.io:26380", "SentinelMasterName": "senkv6380", "DbNumber": 8, "AuthPass": null, "IsSentinel": true, "IsStrictPool": true, "MaxPoolSize": 600, "MaxPoolTimeout": 1000, "WaitBeforeForcingMasterFailover": 1800}, "RedisCache": {"Servers": "dc2d-fnb-infra-01.citigo.io:26379;dc2d-fnb-infra-02.citigo.io:26379;dc2d-fnb-infra-03.citigo.io:26379", "SentinelMasterName": "senkv6379", "DbNumber": 8, "AuthPass": null, "IsSentinel": true, "IsStrictPool": true, "MaxPoolSize": 600, "MaxPoolTimeout": 1000, "WaitBeforeForcingMasterFailover": 1800}, "AmazonS3": {"BucketName": "kvfnb-dev-userdata", "Region": "han01", "AccessKey": "", "SecretKey": "", "UseInstanceProfile": false, "UseCompatibleMode": true, "CompatibleEndpoint": "https://han01.vstorage.vngcloud.vn", "CompatibleAccessKey": "bf19d59437025caabfba66f37c03ff1f", "CompatibleSecretKey": "fcf96a3fc675ae3afd1c5417a2adfa46", "ForcePathStyle": true, "ProxyUrl": "", "BaseKeyPrefix": "uploads/", "UploadPartSize": 5242880, "DefaultACL": "Private", "StorageClass": "Standard", "EncryptionMethod": "None", "CacheControlHeader": "public, max-age=31536000, no-transform", "CloudfrontUrl": "https://cdn-fnb-dev-userdata.kvfnb.vip/"}, "Kma": {"CmaOpenDomainEndpoint": "https://open-kma-dev.kvip.fun", "ApiKey": "fJYo7iA3ZhUFZnoyvUGCfj77l6g5dmUqLe9jymH9rdY4caVm.r2zgCh6AmMRjtlqTq3w7bQ", "Timeout": 5000, "RetryCount": 3}, "OtpConfig": {"OtpSecret": "kv_fnb-$#%~~", "OtpIssuer": "KvFnB", "MaximumSmsRequestFromRetailerPerDay": 100, "SmsCountKeyPrefix": "cache:smscount_", "OtpIssuser": "KvFnB", "OtpMessageTemplate": "Mã xác thực OTP của bạn là {0}. Mã có hiệu lực trong 5 phút. Không cung cấp mã này cho bất kỳ ai khác. Nếu bạn không yêu cầu mã này, vui lòng bỏ qua tin nhắn này."}, "KafkaProducer": {"BootstrapServers": "dc2d-fnb-infra-01.citigo.io:9092,dc2d-fnb-infra-02.citigo.io:9092,dc2d-fnb-infra-03.citigo.io:9092", "Topics": {"SmSTopic": "inpay-kvfnb-sms"}}, "TfaRedisMessagequeue": "redis://dc2d-fnb-infra-01.citigo.io:6382?db=4&amp;MaxPoolSize=100&amp;IsSentinel=false", "ProxyUrl": "http://***********:3128", "ServiceStackJwtSettings": {"AuthKeyBase64": "2XVYGzZp8HB/8w9g5jsrNR4RfgvCe37SXYoyoU3swfU=", "AesIvBase64": "3Gkbi74FCKyi7uUGzvfyQg==", "PrivateKeyXml": "<RSAKeyValue><Modulus>sBxoiezoLcFiVRPU6r8QFuUVJsXI+6hIlMSEDs9RgqK/h07I36H/fu099aGQ2EcuH33pTr85Ig8nQh0G0spGgQpWbzppXJILzItxrCzfDvXIJ4AyW9zuOEX8hkXy+agq8x8l5lIK2bEBMOJ2fzuW4m7Ynlfx8MfS0PqtASwcS9R0lDR1HCXVPeMrjfR2b5pcWe2ccoORPb2nK7+abnAaQGaycyKtYri4rPn43wuFk2XsdeB1kD9+BUKQ/FW57v0BLjT7OMCanxYLtes6qT4ayILyM/jADmUluy2D8jtYVL/4jSTi0V2fWrt5Vwez5AP1dPY+IEYqRuCaSVNTRr98zw==</Modulus><Exponent>AQAB</Exponent><P>/b+pKQscOXeVZPcTNgzMc9Jbw5Betc9lpYF2SwwDsO4RJ3ANB5PQM/keM8XqZzyzyDfEKTYkJq+myunPGcIdTqvlbchg5xT+2T9+sp9O9zlOd4YJfnwJ8CnoLEXbaVJWghFL61x+KHC2fzifuiEtxCvjL+6B2d+rU078/RaiGeE=</P><Q>saxouqwwRb9IuvtztBPLao+ajbyVOwMWOHtPMKnamE+1qOJQHGRMlghDSxGG3AljM1pEqX4059HF/pFwhs1G8v1dmlE6PjNxcKugAoh45RNO+lWY+AYKPvkrNc4sEpmCQUJZdF0bbgePmPf1O2QEMtL+r/RRvXGfzDzWiOBhTK8=</Q><DP>4wV4yNnB1MBijsRnEbJ9sRoBM4SU18DIRjz/osELBF7aRhh/78mVOwqktNlbkCXFC1qpS92+lkYMyfpJadg79EjiqUffOp+UKrYeGp1hPtqsk07+a4V6Nr6nlASj+yTJdPEYpWhJDtFY1ZXJgfNxSNOjHR0cW9s7IvvGaIC5YsE=</DP><DQ>gfY8/R7qiHos9yjrL3OzPZUhVj/1YJ8XZ+U6vbvgu7zCgnBYbQwowS32GzkMznLHb06+HsTEajWVgGpeRxxso10VVMXJtqnmdqXzeybcV6PSB2cJZ5Lzd7DHHKpcrpY/IwEnceiRIeDB9wCZDwJP1+Viis5J5Cry2nA99Q073t0=</DQ><InverseQ>p3LFdQBCt18juiGrBtqdeRAJFwLBji2TQ+E7l7ZLkrdLxYgJhbYAhBoBKeRcD5UYdLDt8CkRlUQD+dlULWI8d0dJITEYvtMrVEBpeoSwdVN+nvHavYHSKJcxesoRv5RiHFqIZDti6XfscBKMotDjQqXDdjS8+tCE+ShNDQUPU54=</InverseQ><D>NPDFSDkMbrzEh5jHl63J82f4HMi2K1iiQP52SFFwSloMQ2uenQOA37IHvzBLb4iuR8889Gwti5yMi6ZYJ8OgwCfQZe1kQOyyWVHN4Rk4ELI38g/qk4ztm8MGL4MVkJFKH/3MEdm140z/c6eQQZ0zNWxm9+quE1iueh3xJfoRo4V/uRnzJSLkZoXQwG+FDzdLkOApGeh8K+IBzXppA8jp0OUpP/rB3Wd0rubOQDzSE5VYetWW4o36FeKM77Qp+n/biuaE1GcZR+vaPtGOjKV0ESCjnWqK/4BTMNQiPD7Q0kQ2zssTKHDIzvLEOp+htLVhzjM/RS+NGABQfmjN2ltLQQ==</D></RSAKeyValue>", "PublicKeyXml": "<RSAKeyValue><Modulus>sBxoiezoLcFiVRPU6r8QFuUVJsXI+6hIlMSEDs9RgqK/h07I36H/fu099aGQ2EcuH33pTr85Ig8nQh0G0spGgQpWbzppXJILzItxrCzfDvXIJ4AyW9zuOEX8hkXy+agq8x8l5lIK2bEBMOJ2fzuW4m7Ynlfx8MfS0PqtASwcS9R0lDR1HCXVPeMrjfR2b5pcWe2ccoORPb2nK7+abnAaQGaycyKtYri4rPn43wuFk2XsdeB1kD9+BUKQ/FW57v0BLjT7OMCanxYLtes6qT4ayILyM/jADmUluy2D8jtYVL/4jSTi0V2fWrt5Vwez5AP1dPY+IEYqRuCaSVNTRr98zw==</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>", "ExpireTokensInDays": 28, "ExpireTokensRefreshInDays": 60, "Issuer": "kvssjwt", "EncryptPayload": false, "RequireSecureConnection": false, "HashAlgorithm": "RS256", "RequireHashAlgorithm": false}, "AllowedHosts": "*", "Kfin": {"EndPoint": "https://payment-dev.kiotfinance.vn/api-sdk/v1.0.0", "PrivateKey": "33733676397924423f4528482b4d6251655468576d5a7134743777217a25432A", "InternalEndPoint": "https://payment-private-dev.kiotfinance.vn/api-sdk/v1.0.0", "InternalPrivateKey": "MlqfkUTIZkgF8Xwmwa812BTwx5OFPKAs3HXkbbK4GWm981Ex", "ProxyUrl": "http://***********:3128"}, "Kafka": {"BootstrapServers": "dc2d-fnb-infra-01.citigo.io:9092,dc2d-fnb-infra-02.citigo.io:9092,dc2d-fnb-infra-03.citigo.io:9092", "DefaultTopic": "fnb-digital-marketing-phoenix", "ClientId": "kvfnb-core", "MessageMaxBytes": 104857600, "RetryAttempts": 3}}