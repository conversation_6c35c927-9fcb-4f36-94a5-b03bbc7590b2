﻿using System.Linq.Expressions;
using KvFnB.Core.Domain;

namespace KvFnB.Modules.Supplier.Domain.Specifications
{
    public class SupplierByIdSpecification : Specification<Models.Supplier>
    {
        private readonly int _supplierId;

        public SupplierByIdSpecification(int supplierId)
        {
            _supplierId = supplierId;
        }

        public override Expression<Func<Models.Supplier, bool>> GetExpression()
        {
            return entity => entity.Id == _supplierId && entity.IsDeleted == false;
        }
    }
}
