﻿using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;

namespace KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.ActiveSupplier
{
    public class ActiveSupplierValidator : Validator<ActiveSupplierRequest>
    {
        public ActiveSupplierValidator()
        {
            RuleFor(x => x.SupplierId)
                .GreaterThan(0, "Supplier ID must be greater than zero.");
        }
    }
}
