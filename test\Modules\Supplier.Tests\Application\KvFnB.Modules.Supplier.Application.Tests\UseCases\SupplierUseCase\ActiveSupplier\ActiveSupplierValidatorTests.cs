﻿using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.ActiveSupplier;
using Xunit;
using Assert = Xunit.Assert;

namespace KvFnB.Modules.Supplier.Application.Tests.UseCases.SupplierUseCase.ActiveSupplier
{
    public class ActiveSupplierValidatorTests
    {
        private readonly ActiveSupplierValidator _validator;

        public ActiveSupplierValidatorTests()
        {
            _validator = new ActiveSupplierValidator();
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        public void Validate_ShouldReturnError_WhenSupplierIdIsInvalid(int supplierId)
        {
            // Arrange
            var request = new ActiveSupplierRequest { SupplierId = supplierId };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("Supplier ID must be greater than zero.", result.Errors);
        }

        [Fact]
        public void Validate_ShouldPass_WhenSupplierIdIsValid()
        {
            // Arrange
            var request = new ActiveSupplierRequest { SupplierId = 123 };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }
    }
}
