﻿using AutoMapper;
using KvFnB.Modules.Supplier.Application.Dtos;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.ActiveSupplier;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeActiveSupplier;
using KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeleteSupplier;

namespace KvFnB.Modules.Supplier.Infrastructure.Mapping
{
    public class SupplierMappingProfile : Profile
    {
        public SupplierMappingProfile()
        {
            // Supplier to DTO mappings
            CreateMap<Domain.Models.Supplier, SupplierDto>()
                .ForMember(dest => dest.CreatedDate, opt => opt.Ignore())
                .ForMember(dest => dest.TotalPayment, opt => opt.Ignore())
                .ForMember(dest => dest.TotalPaymentWithoutReturn, opt => opt.Ignore())
                .ForMember(dest => dest.PurchaseOrderCount, opt => opt.Ignore());

            // ActiveSupplier mappings
            CreateMap<Domain.Models.Supplier, ActiveSupplierResponse>();

            // DeActiveSupplier mappings
            CreateMap<Domain.Models.Supplier, DeActiveSupplierResponse>();

            // DeleteSupplier mappings
            CreateMap<Domain.Models.Supplier, DeleteSupplierResponse>();
        }
    }
}
