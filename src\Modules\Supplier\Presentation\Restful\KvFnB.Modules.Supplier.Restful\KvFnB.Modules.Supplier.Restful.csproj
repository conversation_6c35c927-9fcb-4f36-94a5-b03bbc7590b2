﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\Shared\KvFnB.Shared.csproj" />
    <ProjectReference Include="..\..\..\Application\KvFnB.Modules.Supplier.Application\KvFnB.Modules.Supplier.Application.csproj" />
    <ProjectReference Include="..\..\..\Domain\KvFnB.Modules.Supplier.Domain\KvFnB.Modules.Supplier.Domain.csproj" />
    <ProjectReference Include="..\..\..\Infrastructure\KvFnB.Modules.Supplier.Infrastructure\KvFnB.Modules.Supplier.Infrastructure.csproj" />
	<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="7.2.0" />
  </ItemGroup>

</Project>
