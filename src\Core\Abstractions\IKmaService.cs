using KvFnB.Core.Models;

namespace KvFnB.Core.Abstractions
{
    /// <summary>
    /// Interface for KMA service related operations
    /// </summary>
    public interface IKmaService
    {
        /// <summary>
        /// Verifies a user's phone number using the KMA API
        /// </summary>
        /// <param name="phoneNumber">The phone number to verify</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if the phone number is valid, false otherwise</returns>
        Task<bool> VerifyPhoneNumberAsync(int tenantId, string phoneNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Checks the digital signature status for a merchant
        /// </summary>
        /// <param name="retailerId">The ID of the retailer to check</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The digital signature quota information containing total free and remaining free signatures</returns>
        Task<DigitalSignatureResponse> CheckDigitalSignatureAsync(int retailerId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the e-invoice quota for a merchant
        /// </summary>
        /// <param name="taxCode">The merchant's tax code</param>
        /// <param name="retailerId">The ID of the retailer</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The e-invoice quota information</returns>
        Task<KmaEInvoiceQuota> GetEInvoiceQuotaAsync(string taxCode, int retailerId, CancellationToken cancellationToken = default);
    }
} 