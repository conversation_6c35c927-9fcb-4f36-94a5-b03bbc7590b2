using KvFnB.Core.Abstractions;
using KvFnB.Core.Constants;
using KvFnB.Core.Contracts;
using KvFnB.Core.Domain.Services;
using KvFnB.Core.Validation;
using KvFnB.Modules.Menu.Domain.Events;
using KvFnB.Modules.Menu.Domain.Models;
using KvFnB.Modules.Menu.Domain.Repositories;

namespace KvFnB.Modules.Menu.Application.UseCases.ProductUseCase.DeleteProduct
{
    /// <summary>
    /// Implements the DeleteProduct use case
    /// </summary>
    public class DeleteProductUseCase
    {
        private readonly IValidator<DeleteProductRequest> _validator;
        private readonly IProductRepository _productRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly ICodeGenerationService _codeGenerationService;
        

        public DeleteProductUseCase(
            IValidator<DeleteProductRequest> validator,
            IProductRepository productRepository,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger logger,
            ICodeGenerationService codeGenerationService)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _productRepository = productRepository ?? throw new ArgumentNullException(nameof(productRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _codeGenerationService = codeGenerationService ?? throw new ArgumentNullException(nameof(codeGenerationService));
        }

        /// <summary>
        /// Deletes a product by marking it as deleted and updating its code
        /// </summary>
        /// <param name="request">The delete product request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result containing information about the deleted product</returns>
        public async Task<Result<DeleteProductResponse>> ExecuteAsync(
            DeleteProductRequest request,
            CancellationToken cancellationToken = default)
        {
            // 1. Validate request
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                _logger.Warning($"Product deletion validation failed: {string.Join(", ", validationResult.Errors)}");
                return Result<DeleteProductResponse>.Failure(validationResult.Errors);
            }

            try
            {
                // 2. Retrieve the product
                var product = await _productRepository.GetAsync(request.Id, cancellationToken);
                if (product == null)
                {
                    _logger.Warning($"Product with ID {request.Id} not found for deletion");
                    return Result<DeleteProductResponse>.Failure($"Product with ID {request.Id} not found");
                }

                // if product has deleted, return failure
                if (product.IsDeleted.GetValueOrDefault())
                {
                    return Result<DeleteProductResponse>.Failure("Product already deleted");
                }

                // 3. Mark code as deleted
                if (!string.IsNullOrEmpty(product.Code))
                {
                    var updatedCode = await _codeGenerationService.GenerateDeletionCodeAsync<Product>(product.TenantId, product.Code, "Product");
                    product.UpdateCode(updatedCode);
                    _logger.Information($"Product code updated for deletion: {product.Code} -> {updatedCode}");
                }

                // 4. Mark the product as deleted
                product.IsDeleted = true;
                
                // 5. Update the product
                product = await _productRepository.UpdateAsync(product, cancellationToken);

                // 6. Commit changes
                await _unitOfWork.CommitAsync(cancellationToken);

             
                // 7. Emit a domain event to invalidate cache
                product.AddDomainEvent(new ProductDeletedEvent(product.Id));

                // 8. Store product data for response before deletion
                var response = _mapper.Map<DeleteProductResponse>(product);

                _logger.Information($"Emitted domain event for deleted product {product.Id}");

                // 9. Return success result
                return Result<DeleteProductResponse>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.Error($"Error deleting product with ID {request.Id}", ex);
                return Result<DeleteProductResponse>.Failure(ErrorMessages.InternalServerError);
            }
        }
    }
} 