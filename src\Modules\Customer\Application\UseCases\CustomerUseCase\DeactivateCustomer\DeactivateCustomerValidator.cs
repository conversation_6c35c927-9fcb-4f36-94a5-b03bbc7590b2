using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.DeactivateCustomer
{
    /// <summary>
    /// Validates the DeactivateCustomer request
    /// </summary>
    public class DeactivateCustomerValidator : Validator<DeactivateCustomerRequest>
    {
        public DeactivateCustomerValidator()
        {
            // Validate that customer ID is valid
            RuleFor(x => x.CustomerId)
                .NotNull("Customer ID is required")
                .GreaterThan(0, "Customer ID must be greater than zero");
        }
    }
} 