using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;

namespace KvFnB.Modules.Payment.Application.UseCases.CheckDigitalSignatureStatus;

public class CheckDigitalSignatureUseCase : UseCaseBase<CheckDigitalSignatureRequest, CheckDigitalSignatureResponse>
{
    private readonly IValidator<CheckDigitalSignatureRequest> _validator;
    private readonly IKmaService _kmaService;
    private readonly ILogger _logger;
    private readonly ITenantProvider _tenantProvider;

    public CheckDigitalSignatureUseCase(
        IValidator<CheckDigitalSignatureRequest> validator,
        IKmaService kmaService,
        ILogger logger,
        ITenantProvider tenantProvider)
    {
        _validator = validator ?? throw new ArgumentNullException(nameof(validator));
        _kmaService = kmaService ?? throw new ArgumentNullException(nameof(kmaService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
    }

    public override async Task<Result<CheckDigitalSignatureResponse>> ExecuteAsync(
        CheckDigitalSignatureRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate request
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Result<CheckDigitalSignatureResponse>.Failure(validationResult.Errors);
            }

            // Get retailerId from tenant provider
            var retailerId = _tenantProvider.GetTenantId() ?? 0;

            // Call KMA service
            var digitalSignatureResponse = await _kmaService.CheckDigitalSignatureAsync(
                retailerId,
                cancellationToken);

            var result = new CheckDigitalSignatureResponse
            {
                TotalFree = digitalSignatureResponse.TotalFree,
                RemainFree = digitalSignatureResponse.RemainFree
            };

            return Result<CheckDigitalSignatureResponse>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.Error($"Error checking digital signature status: {ex.Message}", ex);
            return Result<CheckDigitalSignatureResponse>.Failure(
                "Failed to check digital signature status");
        }
    }
} 