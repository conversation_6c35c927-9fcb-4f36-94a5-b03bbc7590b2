using Microsoft.AspNetCore.Mvc;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.DeleteCustomer;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetCustomerDetail;
using Microsoft.AspNetCore.Http;
using KvFnB.Core.Contracts;
using KvFnB.Shared.Filters;
using KvFnB.Core.Abstractions;
using Swashbuckle.AspNetCore.Annotations;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetListCustomer;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.CreateCustomer;

using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.DeactivateCustomer;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.ActivateCustomer;
using KvFnB.Core.Exceptions;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetListCustomerDebt;
namespace KvFnB.Modules.Customer.Restful.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class CustomerController : BaseApi
{
    private readonly IUseCaseFactory _useCaseFactory;
    private readonly IPermissionService _permissionService;
    private readonly ITenantProvider _tenantProvider;

    public CustomerController(
        IHttpContextAccessor httpContextAccessor,
        IUseCaseFactory useCaseFactory,
        IPermissionService permissionService,
        ITenantProvider tenantProvider) : base(httpContextAccessor)
    {
        _useCaseFactory = useCaseFactory ?? throw new ArgumentNullException(nameof(useCaseFactory));
        _permissionService = permissionService ?? throw new ArgumentNullException(nameof(permissionService));
        _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
    }

    /// <summary>
    /// Get detailed information about a specific customer.
    /// </summary>
    [HttpGet("{id}")]
    [SwaggerOperation(
        Summary = "Get customer details",
        Description = "Retrieves detailed information about a specific customer by their ID.",
        OperationId = "GetCustomerDetail",
        Tags = new[] { "Customer" }
    )]
    [SwaggerResponse(StatusCodes.Status200OK, "Successfully retrieved the customer details", typeof(GetCustomerDetailResponse))]
    [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid customer ID or customer not found", typeof(ErrorResponse))]
    [SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized")]
    [SwaggerResponse(StatusCodes.Status404NotFound, "Customer not found")]
    public async Task<IActionResult> GetCustomerDetail([FromRoute] long id, CancellationToken cancellationToken)
    {
        var useCase = _useCaseFactory.GetUseCase<GetCustomerDetailUseCase>();
        var result = await useCase.ExecuteAsync(new GetCustomerDetailRequest(id), cancellationToken);
        return result.IsSuccess ? Success(result) : Failure(result);
    }

    /// <summary>
    /// Get a list of customers with filtering, sorting, and paging.
    /// </summary>
    [HttpGet]
    [SwaggerOperation(
        Summary = "Get list of customers",
        Description = "Retrieves a list of customers with filtering, sorting, and paging options.",
        OperationId = "GetListCustomer",
        Tags = new[] { "Customer" }
    )]
    [SwaggerResponse(StatusCodes.Status200OK, "Successfully retrieved the list of customers", typeof(PagingResponse<GetListCustomerResponse>))]
    [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid request parameters")]
    [SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized")]
    public async Task<ActionResult<PagingResponse<GetListCustomerResponse>>> GetList([FromQuery] GetListCustomerRequest request)
    {
        var useCase = _useCaseFactory.GetUseCase<GetListCustomerUseCase>();
        var result = await useCase.ExecuteAsync(request);
        if (!result.IsSuccess)
        {
            return BadRequest(new { message = result.ErrorMessage });
        }
        return Ok(result.Value);
    }

    /// <summary>
    /// Get a list of debt records for a specific customer.
    /// </summary>
    [HttpGet("{customerId}/debts")]
    [SwaggerOperation(
        Summary = "Get customer debt list",
        Description = "Retrieves a list of debt records for a specific customer with pagination.",
        OperationId = "GetListCustomerDebt",
        Tags = new[] { "Customer" }
    )]
    [SwaggerResponse(StatusCodes.Status200OK, "Successfully retrieved the list of customer debts", typeof(GetListCustomerDebtResponse))]
    [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid request parameters", typeof(ErrorResponse))]
    [SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized")]
    public async Task<IActionResult> GetListCustomerDebt(
        [FromRoute] long customerId, 
        [FromQuery] int skip = 0, 
        [FromQuery] int take = 10,
        [FromQuery] DateTime? startDate = null, 
        [FromQuery] DateTime? endDate = null, 
        CancellationToken cancellationToken = default)
    {
        var request = new GetListCustomerDebtRequest
        {
            CustomerId = customerId,
            Skip = skip,
            Take = take,
            StartDate = startDate,
            EndDate = endDate
        };
        
        var useCase = _useCaseFactory.GetUseCase<GetListCustomerDebtUseCase>();
        var result = await useCase.ExecuteAsync(request, cancellationToken);
        return result.IsSuccess ? Success(result) : Failure(result);
    }

    /// <summary>
    /// Delete a customer by ID.
    /// </summary>
    [HttpDelete("{id}")]
    [SwaggerOperation(
        Summary = "Delete customer",
        Description = "Deletes a customer by their ID.",
        OperationId = "DeleteCustomer",
        Tags = new[] { "Customer" }
    )]
    [SwaggerResponse(StatusCodes.Status200OK, "Successfully deleted the customer", typeof(DeleteCustomerResponse))]
    [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid customer ID or customer not found", typeof(ErrorResponse))]
    [SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized")]
    [SwaggerResponse(StatusCodes.Status403Forbidden, "Forbidden - No permission to delete this customer")]
    public async Task<IActionResult> DeleteCustomer([FromRoute] long id, CancellationToken cancellationToken)
    {
        var request = new DeleteCustomerRequest { Id = id };
        await CheckPermission("Customer_Delete");
        var useCase = _useCaseFactory.GetUseCase<DeleteCustomerUseCase>();
        var result = await useCase.ExecuteAsync(request, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(new ErrorResponse(result.ErrorMessage ?? "Failed to delete customer", result.ValidationErrors));
        }

        return Ok(result.Value);
    }

    /// <summary>
    /// Deactivate a customer by ID.
    /// </summary>
    [HttpPost("inactive")]
    [SwaggerOperation(
        Summary = "Deactivate customer",
        Description = "Deactivates a customer by their ID.",
        OperationId = "DeactivateCustomer",
        Tags = new[] { "Customer" }
    )]
    [SwaggerResponse(StatusCodes.Status200OK, "Successfully deactivated the customer", typeof(DeactivateCustomerResponse))]
    [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid customer ID or customer not found", typeof(ErrorResponse))]
    [SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized")]
    [SwaggerResponse(StatusCodes.Status403Forbidden, "Forbidden - No permission to deactivate this customer")]
    public async Task<ActionResult<Result<DeactivateCustomerResponse>>> DeactivateCustomer([FromBody] DeactivateCustomerRequest request, CancellationToken cancellationToken)
    {
        await CheckPermission("Customer_Update");
        var useCase = _useCaseFactory.GetUseCase<DeactivateCustomerUseCase>();
        var result = await useCase.ExecuteAsync(request, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(new ErrorResponse(result.ErrorMessage ?? "Failed to deactivate customer", result.ValidationErrors));
        }

        return Ok(result);
    }

    /// <summary>
    /// Activate a customer by ID.
    /// </summary>
    [HttpPost("active")]
    [SwaggerOperation(
        Summary = "Activate customer",
        Description = "Activates a customer by their ID.",
        OperationId = "ActivateCustomer",
        Tags = new[] { "Customer" }
    )]
    [SwaggerResponse(StatusCodes.Status200OK, "Successfully activated the customer", typeof(ActivateCustomerResponse))]
    [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid customer ID or customer not found", typeof(ErrorResponse))]
    [SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized")]
    [SwaggerResponse(StatusCodes.Status403Forbidden, "Forbidden - No permission to activate this customer")]
    public async Task<ActionResult<Result<ActivateCustomerResponse>>> ActivateCustomer([FromBody] ActivateCustomerRequest request, CancellationToken cancellationToken)
    {
        await CheckPermission("Customer_Update");
        var useCase = _useCaseFactory.GetUseCase<ActivateCustomerUseCase>();
        var result = await useCase.ExecuteAsync(request, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(new ErrorResponse(result.ErrorMessage ?? "Failed to activate customer", result.ValidationErrors));
        }

        return Ok(result);
    }

    /// Creates a new customer.
    /// </summary>
    [HttpPost]
    [SwaggerOperation(
        Summary = "Create a new customer",
        Description = "Creates a new customer with the provided information.",
        OperationId = "CreateCustomer",
        Tags = new[] { "Customer" }
    )]
    [SwaggerResponse(StatusCodes.Status201Created, "Successfully created the customer", typeof(CreateCustomerResponse))]
    [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid customer data", typeof(ErrorResponse))]
    [SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized")]
    public async Task<IActionResult> CreateCustomer([FromBody] CreateCustomerRequest request, CancellationToken cancellationToken)
    {
        await CheckPermission("Customer_Create");
        var useCase = _useCaseFactory.GetUseCase<CreateCustomerUseCase>();
        var result = await useCase.ExecuteAsync(request, cancellationToken);
        if (!result.IsSuccess)
        {
            return Failure(result);
        }
        
        return CreatedAtAction(nameof(GetCustomerDetail), new { id = result.Value!.Id }, result.Value);
    }

    private async Task CheckPermission(string permissionKey)
    {
        if (_permissionService.IsAdmin) return;
        var permissions = await _permissionService.GetUserPermissionsAsync();
        var branchIds = permissions.FirstOrDefault(p => p.Key == permissionKey).Value ?? new List<long>();

        if (branchIds.Count == 0 || !branchIds.Contains(_tenantProvider.GetBranchId() ?? 0) )
        {
            throw new ForbiddenException(string.Format("User does not have {0} permission.", permissionKey));
        }
    }
}