using System;
using System.Text.Json.Serialization;
using KvFnB.Core.Domain.Commands;

namespace KvFnB.Modules.Customer.Domain.Commands
{
    /// <summary>
    /// Command sent when a customer is successfully registered as a member.
    /// Used for digital marketing integrations like Zalo.
    /// </summary>
    public class DigitalRegisterMemberSuccessedEvent : BaseDigitalCommand
    {
        /// <summary>
        /// Customer's unique identifier.
        /// </summary>
        [JsonPropertyName("customerId")]
        public long CustomerId { get; private set; }
        
        /// <summary>
        /// Customer's contact phone number.
        /// </summary>
        [JsonPropertyName("contactNumber")]
        public string ContactNumber { get; private set; }
        
        /// <summary>
        /// Customer's full name.
        /// </summary>
        [JsonPropertyName("customerName")]
        public string CustomerName { get; private set; }
        
        /// <summary>
        /// Customer's accumulated reward points.
        /// </summary>
        [JsonPropertyName("rewardPoint")]
        public decimal RewardPoint { get; private set; }
        
        /// <summary>
        /// Customer's group name(s), pipe-delimited if multiple.
        /// </summary>
        [JsonPropertyName("groupName")]
        public string GroupName { get; private set; }
        
        /// <summary>
        /// Customer's unique code in the system.
        /// </summary>
        [JsonPropertyName("customerCode")]
        public string CustomerCode { get; private set; }

        /// <summary>
        /// Creates a new instance of the DigitalRegisterMemberSuccessedEvent.
        /// </summary>
        /// <param name="commandIssuer">The system component that issued the command (e.g., "KiotViet-Webman-Api")</param>
        /// <param name="retailerCode">Retailer code identifier</param>
        /// <param name="retailerId">Retailer ID</param>
        /// <param name="branchId">Branch ID where the registration occurred</param>
        /// <param name="groupId">Group ID</param>
        /// <param name="userId">User ID who created the customer</param>
        /// <param name="customerId">Customer's unique identifier</param>
        /// <param name="contactNumber">Customer's contact phone number</param>
        /// <param name="customerName">Customer's full name</param>
        /// <param name="rewardPoint">Customer's reward points</param>
        /// <param name="customerCode">Customer's unique code</param>
        /// <param name="groupName">Customer's group name(s)</param>
        public DigitalRegisterMemberSuccessedEvent(
            string commandIssuer,
            string retailerCode,
            int retailerId,
            long branchId,
            int groupId,
            long userId,
            long customerId,
            string contactNumber,
            string customerName,
            decimal rewardPoint,
            string customerCode,
            string groupName) : base(commandIssuer, retailerCode, retailerId, branchId, groupId, userId)
        {
            if (customerId <= 0)
                throw new ArgumentException("Customer ID must be greater than zero", nameof(customerId));
                
            if (string.IsNullOrWhiteSpace(customerName))
                throw new ArgumentException("Customer name cannot be null or empty", nameof(customerName));
                
            if (string.IsNullOrWhiteSpace(contactNumber))
                throw new ArgumentException("Contact number cannot be null or empty", nameof(contactNumber));
                
            if (string.IsNullOrWhiteSpace(customerCode))
                throw new ArgumentException("Customer code cannot be null or empty", nameof(customerCode));

            CustomerId = customerId;
            ContactNumber = contactNumber;
            CustomerName = customerName;
            RewardPoint = rewardPoint;
            GroupName = groupName ?? "Đã là thành viên";
            CustomerCode = customerCode;
        }
    }
} 