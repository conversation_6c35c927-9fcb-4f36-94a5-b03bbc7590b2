using System;
using System.ComponentModel;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetListCustomerDebt
{
    /// <summary>
    /// Request model for retrieving a list of customer debts with pagination and date filtering
    /// </summary>
    public record GetListCustomerDebtRequest
    {
        /// <summary>
        /// The unique identifier of the customer
        /// </summary>
        [Description("The unique identifier of the customer.")]
        public long CustomerId { get; init; }
        
        /// <summary>
        /// Number of items to skip for pagination (default: 0)
        /// </summary>
        [Description("Number of items to skip for pagination.")]
        public int Skip { get; init; } = 0;
        
        /// <summary>
        /// Number of items to take for pagination (default: 10, max: 100)
        /// </summary>
        [Description("Number of items to take for pagination.")]
        public int Take { get; init; } = 10;
        
        /// <summary>
        /// Optional start date for filtering debts
        /// </summary>
        [Description("Optional start date for filtering debts.")]
        public DateTime? StartDate { get; init; }
        
        /// <summary>
        /// Optional end date for filtering debts
        /// </summary>
        [Description("Optional end date for filtering debts.")]
        public DateTime? EndDate { get; init; }
    }
} 