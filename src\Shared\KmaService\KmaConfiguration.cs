namespace KvFnB.Shared.KmaService
{
    /// <summary>
    /// Configuration for KMA service
    /// </summary>
    public class KmaConfiguration
    {
        /// <summary>
        /// Endpoint for the CMA Open Domain API
        /// </summary>
        public string CmaOpenDomainEndpoint { get; set; } = string.Empty;

        /// <summary>
        /// API key for authentication
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;
        
        /// <summary>
        /// Timeout in milliseconds
        /// </summary>
        public int Timeout { get; set; } = 5000;

    }
} 