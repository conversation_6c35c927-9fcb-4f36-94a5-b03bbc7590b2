using KvFnB.Modules.Customer.Domain.Models;
using KvFnB.Shared.Persistence.ShardingDb.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KvFnB.Shared.Persistence.ShardingDb.EntityTypeConfigurations
{
    public class CustomerGroupEntityTypeConfiguration : BaseEntityTypeConfiguration<CustomerGroup>
    {
        public override void Configure(EntityTypeBuilder<CustomerGroup> builder)
        {
            base.Configure(builder);
            builder.ToTable("CustomerGroup");

            builder.Property(p => p.IsDeleted)
               .IsRequired(false)
               .HasColumnName("isDeleted")
               .HasColumnType(SqlServerColumnTypes.BOOLEAN);

            builder.Ignore(p => p.DeletedAt);
            builder.Ignore(p => p.DeletedBy);
            builder.Ignore(c => c.Created<PERSON>t);
            builder.Ignore(c => c.ModifiedAt);
        }
    }
}