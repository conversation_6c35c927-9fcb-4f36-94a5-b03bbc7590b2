using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using KvFnB.Core.Abstractions;
using KvFnB.Modules.Customer.Domain.Models;
using KvFnB.Modules.Customer.Domain.Repositories;
using KvFnB.Shared.Persistence.ShardingDb;
using Microsoft.EntityFrameworkCore;

namespace KvFnB.Modules.Customer.Infrastructure.Persistence
{
    public class CustomerGroupRepository : ICustomerGroupRepository
    {
        private readonly ShardingDbContext _context;

        public CustomerGroupRepository(IUnitOfWork unitOfWork)
        {
            _context = unitOfWork as ShardingDbContext ?? throw new ArgumentException("UnitOfWork must be of type ShardingDbContext", nameof(unitOfWork));
        }

        public async Task<IEnumerable<CustomerGroup>> GetByIdsAsync(IEnumerable<int> ids, CancellationToken cancellationToken = default)
        {
            if (ids == null || !ids.Any())
                return Enumerable.Empty<CustomerGroup>();
                
            return await _context.Set<CustomerGroup>()
                .Where(g => ids.Contains(g.Id) && (g.IsDeleted == null || g.IsDeleted == false))
                .ToListAsync(cancellationToken);
        }
    }
} 