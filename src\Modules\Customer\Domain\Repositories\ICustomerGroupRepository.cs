using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using KvFnB.Modules.Customer.Domain.Models;

namespace KvFnB.Modules.Customer.Domain.Repositories
{
    /// <summary>
    /// Repository for customer group operations
    /// </summary>
    public interface ICustomerGroupRepository
    {
        /// <summary>
        /// Gets customer groups by their IDs
        /// </summary>
        /// <param name="groupIds">The IDs of the groups to retrieve</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Collection of customer groups</returns>
        Task<IEnumerable<CustomerGroup>> GetByIdsAsync(IEnumerable<int> groupIds, CancellationToken cancellationToken = default);
    }
} 