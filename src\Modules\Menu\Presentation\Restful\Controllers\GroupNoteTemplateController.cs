using KvFnB.Modules.Menu.Application.UseCases.GroupNoteTemplateUseCase.CreateGroupNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.GroupNoteTemplateUseCase.DeleteGroupNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.GroupNoteTemplateUseCase.GetListGroupNoteTemplate;
using KvFnB.Modules.Menu.Application.UseCases.GroupNoteTemplateUseCase.UpdateGroupNoteTemplate.UpdateGroupName;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using KvFnB.Shared.Filters;
using Microsoft.AspNetCore.Http;
using KvFnB.Core.Contracts;
using KvFnB.Modules.Menu.Application.UseCases.GroupNoteTemplateUseCase.GetAllGroupNoteTemplate;

namespace KvFnB.Modules.Menu.Restful.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class GroupNoteTemplateController : BaseController
    {
        private readonly CreateGroupNoteTemplateUseCase _createUseCase;
        private readonly UpdateGroupNameUseCase _updateNameUseCase;
        private readonly DeleteGroupNoteTemplateUseCase _deleteUseCase;
        private readonly GetListGroupNoteTemplateUseCase _getListUseCase;
        private readonly GetAllGroupNoteTemplateUseCase _getAllUseCase;

        public GroupNoteTemplateController(
            CreateGroupNoteTemplateUseCase createUseCase,
            UpdateGroupNameUseCase updateNameUseCase,
            DeleteGroupNoteTemplateUseCase deleteUseCase,
            GetListGroupNoteTemplateUseCase getListUseCase,
            GetAllGroupNoteTemplateUseCase getAllUseCase,
            IHttpContextAccessor httpContextAccessor
        )
            : base(httpContextAccessor)
        {
            _createUseCase = createUseCase ?? throw new ArgumentNullException(nameof(createUseCase));
            _updateNameUseCase = updateNameUseCase ?? throw new ArgumentNullException(nameof(updateNameUseCase));
            _deleteUseCase = deleteUseCase ?? throw new ArgumentNullException(nameof(deleteUseCase));
            _getListUseCase = getListUseCase ?? throw new ArgumentNullException(nameof(getListUseCase));
            _getAllUseCase = getAllUseCase ?? throw new ArgumentNullException(nameof(getAllUseCase));
        }

        /// <summary>
        /// Creates a new group note template.
        /// </summary>
        /// <param name="request">The request object containing the details of the group note template to create.</param>
        /// <returns>A response indicating the result of the create operation.</returns>
        /// <response code="200">Returns the newly created group note template.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPost]
        [SwaggerOperation(Summary = "Creates a new group note template", Description = "Creates a new group note template with the provided details.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the newly created group note template", typeof(CreateGroupNoteTemplateResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> CreateGroupNoteTemplate([FromBody] CreateGroupNoteTemplateRequest request)
        {
            var result = await _createUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Success(result);
        }

        /// <summary>
        /// Updates the name of an existing group note template.
        /// </summary>
        /// <param name="id">The ID of the group note template to update.</param>
        /// <param name="request">The request object containing the updated name.</param>
        /// <returns>A response indicating the result of the update operation.</returns>
        /// <response code="200">Returns the updated group note template.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPut("{id}/name")]
        [SwaggerOperation(Summary = "Updates the name of an existing group note template", Description = "Updates the name of an existing group note template.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the updated group note template", typeof(UpdateGroupNameResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> UpdateGroupName(long id, [FromBody] UpdateGroupNameRequest request)
        {
            request.Id = id;
            var result = await _updateNameUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Success(result);
        }

        /// <summary>
        /// Deletes an existing group note template.
        /// </summary>
        /// <param name="id">The ID of the group note template to delete.</param>
        /// <returns>A response indicating the result of the delete operation.</returns>
        /// <response code="200">Returns the deleted group note template.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpDelete("{id}")]
        [SwaggerOperation(Summary = "Deletes an existing group note template", Description = "Deletes an existing group note template by ID.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the deleted group note template", typeof(DeleteGroupNoteTemplateResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> DeleteGroupNoteTemplate(long id)
        {
            var request = new DeleteGroupNoteTemplateRequest { Id = id };
            var result = await _deleteUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Success(result);
        }

        /// <summary>
        /// Gets a list of group note templates.
        /// </summary>
        /// <param name="request">The request object containing filtering and paging parameters.</param>
        /// <returns>A response indicating the result of the get operation.</returns>
        /// <response code="200">Returns the list of group note templates.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpGet]
        [SwaggerOperation(Summary = "Gets a list of group note templates", Description = "Gets a paginated list of group note templates.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the list of group note templates", typeof(PagingResponse<GetListGroupNoteTemplateResponse>))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> GetGroupNoteTemplates([FromQuery] GetListGroupNoteTemplateRequest request)
        {
            var result = await _getListUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Success(result);
        }

        [HttpGet("get-all")]
        [SwaggerOperation(Summary = "Gets a list of group note templates", Description = "Gets a paginated list of group note templates.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the list of group note templates", typeof(PagingResponse<GetListGroupNoteTemplateResponse>))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
         public async Task<IActionResult> GetAllGroupNoteTemplates()
        {
            var result = await _getAllUseCase.ExecuteAsync(new GetAllGroupNoteTemplateRequest());
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Success(result);
        }
    }
} 