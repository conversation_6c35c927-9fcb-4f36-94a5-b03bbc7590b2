﻿using KvFnB.Modules.Supplier.Domain.Models;
using KvFnB.Shared.Persistence.ShardingDb.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KvFnB.Shared.Persistence.ShardingDb.EntityTypeConfigurations
{
    public class SupplierEntityTypeConfiguration : BaseEntityTypeConfiguration<Supplier>
    {
        public override void Configure(EntityTypeBuilder<Supplier> builder)
        {
            base.Configure(builder);
            builder.ToTable("Supplier");

            builder.Property(c => c.Id)
                    .UseIdentityColumn()
                    .ValueGeneratedOnAdd()
                    .HasColumnName("Id")
                    .HasColumnType(SqlServerColumnTypes.INT);

            builder.Property(p => p.IsDeleted)
               .IsRequired(false)
               .HasColumnName("isDeleted")
               .HasColumnType(SqlServerColumnTypes.BOOLEAN);


            builder.Ignore(p => p.<PERSON>t);
            builder.Ignore(p => p.DeletedBy);
        }
    }
}
