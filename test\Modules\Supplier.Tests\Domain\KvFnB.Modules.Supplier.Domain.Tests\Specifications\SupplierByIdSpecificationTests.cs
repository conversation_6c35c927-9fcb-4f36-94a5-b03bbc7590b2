﻿using KvFnB.Modules.Supplier.Domain.Specifications;
using Xunit;
using Assert = Xunit.Assert;

namespace KvFnB.Modules.Supplier.Domain.Tests.Specifications
{
    public class SupplierByIdSpecificationTests
    {
        [Fact]
        public void GetExpression_ShouldMatchSupplierWithCorrectId_AndNotDeleted()
        {
            // Arrange
            var spec = new SupplierByIdSpecification(5);
            var expression = spec.GetExpression().Compile();

            var matchingSupplier = new Domain.Models.Supplier { Id = 5, IsDeleted = false };
            var deletedSupplier = new Domain.Models.Supplier { Id = 5, IsDeleted = true };
            var otherSupplier = new Domain.Models.Supplier { Id = 2, IsDeleted = false };

            // Act & Assert
            Assert.True(expression(matchingSupplier));
            Assert.False(expression(deletedSupplier));
            Assert.False(expression(otherSupplier));
        }
    }
}
