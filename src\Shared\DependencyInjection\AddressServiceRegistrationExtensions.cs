using KvFnB.Core.Domain.Repositories;
using KvFnB.Core.Domain.Services;
using KvFnB.Shared.Persistence.Services;
using KvFnB.Shared.Persistence.ShardingDb.Repositories;
using Microsoft.Extensions.DependencyInjection;

namespace KvFnB.Shared.DependencyInjection
{
    /// <summary>
    /// Extension methods for registering address-related services
    /// </summary>
    public static class AddressServiceRegistrationExtensions
    {
        /// <summary>
        /// Adds address services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddAddressServices(this IServiceCollection services)
        {
            // Register repositories
            services.AddScoped<IAddressRepository, AddressRepository>();
            
            // Register domain services
            services.AddScoped<IAddressDomainService, AddressDomainService>();
            
            return services;
        }
    }
} 