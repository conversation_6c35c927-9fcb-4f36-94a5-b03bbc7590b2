using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.GetProductPriceBooks;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.GetProductPriceBookDetail;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateProductPrice;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.CreateProductPriceBook;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.ActiveProductPriceBook;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.DeActiveProductPriceBook;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.DeleteProductPriceBook;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateScopeOfApplication;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateCustomTime;
using KvFnB.Modules.Menu.Application.UseCases.PriceBookUseCase.UpdateProductPriceBook.UpdateBasicInfo;
using Microsoft.Extensions.DependencyInjection;

namespace KvFnB.Modules.Menu.Restful.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class PriceBookController : BaseController
    {
        public PriceBookController(IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
        }

        /// <summary>
        /// Creates a new price book.
        /// </summary>
        /// <param name="request">The request object containing the details of the price book to create.</param>
        /// <returns>A response indicating the result of the create operation.</returns>
        /// <response code="200">Returns the newly created price book.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPost]
        [SwaggerOperation(Summary = "Creates a new price book", Description = "Creates a new price book with the provided details.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the newly created price book", typeof(CreateProductPriceBookResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> CreatePriceBook([FromBody] CreateProductPriceBookRequest request)
        {
            var useCase = HttpContext.RequestServices.GetRequiredService<CreateProductPriceBookUseCase>();
            var result = await useCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Updates the price of a product in a price book or its base price.
        /// </summary>
        /// <param name="request">The request object containing product ID, price book ID, and new price.</param>
        /// <returns>A response indicating the success of the update operation.</returns>
        /// <response code="200">Returns when the price is updated successfully.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPut("product-price")]
        [SwaggerOperation(Summary = "Updates product price", Description = "Updates the price of a product in a specific price book or its base price.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Price updated successfully")]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> UpdateProductPrice([FromBody] UpdateProductPriceRequest request)
        {
            var useCase = HttpContext.RequestServices.GetRequiredService<UpdateProductPriceUseCase>();
            var result = await useCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Deletes a price book.
        /// </summary>
        /// <param name="id">The ID of the price book to delete.</param>
        /// <returns>A response indicating the success of the deletion operation.</returns>
        /// <response code="200">Returns when the price book is deleted successfully.</response>
        /// <response code="400">If the request is invalid or the price book is not found.</response>
        [HttpDelete("{id}")]
        [SwaggerOperation(Summary = "Deletes a price book", Description = "Deletes a price book with the specified ID.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Price book deleted successfully")]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid or the price book is not found")]
        public async Task<IActionResult> DeletePriceBook(long id)
        {
            var useCase = HttpContext.RequestServices.GetRequiredService<DeleteProductPriceBookUseCase>();
            var request = new DeleteProductPriceBookRequest { PriceBookId = id };
            var result = await useCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Activates a price book.
        /// </summary>
        /// <param name="id">The ID of the price book to activate.</param>
        /// <returns>A response indicating the success of the activation operation.</returns>
        /// <response code="200">Returns when the price book is activated successfully.</response>
        /// <response code="400">If the request is invalid or the price book is not found.</response>
        [HttpPut("activate/{id}")]
        [SwaggerOperation(Summary = "Activates a price book", Description = "Activates a price book with the specified ID.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Price book activated successfully")]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid or the price book is not found")]
        public async Task<IActionResult> ActivatePriceBook(long id)
        {
            var useCase = HttpContext.RequestServices.GetRequiredService<ActiveProductPriceBookUseCase>();
            var request = new ActiveProductPriceBookRequest { PriceBookId = id };
            var result = await useCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Deactivates a price book.
        /// </summary>
        /// <param name="id">The ID of the price book to deactivate.</param>
        /// <returns>A response indicating the success of the deactivation operation.</returns>
        /// <response code="200">Returns when the price book is deactivated successfully.</response>
        /// <response code="400">If the request is invalid or the price book is not found.</response>
        [HttpPut("deactivate/{id}")]
        [SwaggerOperation(Summary = "Deactivates a price book", Description = "Deactivates a price book with the specified ID.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Price book deactivated successfully")]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid or the price book is not found")]
        public async Task<IActionResult> DeactivatePriceBook(long id)
        {
            var useCase = HttpContext.RequestServices.GetRequiredService<DeActiveProductPriceBookUseCase>();
            var request = new DeActiveProductPriceBookRequest { PriceBookId = id };
            var result = await useCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Gets a list of price books for a product.
        /// </summary>
        /// <param name="request">The request object containing search and pagination parameters.</param>
        /// <returns>A response containing the list of price books.</returns>
        /// <response code="200">Returns the list of price books.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpGet]
        [SwaggerOperation(Summary = "Gets a list of priceBooks", Description = "Gets a list of priceBooks with pagination and search")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the list of priceBooks", typeof(IEnumerable<GetProductPriceBooksResponse>))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> GetProductPriceBooks([FromQuery] GetProductPriceBooksRequest request)
        {
            var useCase = HttpContext.RequestServices.GetRequiredService<GetProductPriceBooksUseCase>();
            var result = await useCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Gets the price book detail for a specific product and price book.
        /// </summary>
        /// <param name="request">The request object containing PriceBookId and pagination parameters.</param>
        /// <returns>A response containing the price book detail.</returns>
        /// <response code="200">Returns the price book detail.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpGet("detail")]
        [SwaggerOperation(Summary = "Gets price book detail for priceBookId", Description = "Gets the price book detail for a specific price book.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the price book detail", typeof(GetProductPriceBookDetailResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> GetProductPriceBookDetail([FromQuery] GetProductPriceBookDetailRequest request)
        {
            var useCase = HttpContext.RequestServices.GetRequiredService<GetProductPriceBookDetailUseCase>();
            var result = await useCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Updates the Basic Information for a price book.
        /// </summary>
        /// <param name="request">The request object containing the details of the price book to update.</param>
        /// <returns>A response indicating the result of the update operation.</returns>
        /// <response code="200">Returns the updated price book.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPut("basic-information")]
        [SwaggerOperation(Summary = "Updates the basic information for a price book", Description = "Updates the basic information for a price book.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Price book basic information updated successfully")]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> UpdateBasicInformation([FromBody] UpdateBasicInfoRequest request)
        {
            var useCase = HttpContext.RequestServices.GetRequiredService<UpdateBasicInfoUseCase>();
            var result = await useCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Updates the scope of application for a price book.
        /// </summary>
        /// <param name="request">The request object containing the details of the price book to update.</param>
        /// <returns>A response indicating the result of the update operation.</returns>
        /// <response code="200">Returns the updated price book.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPut("scope-of-application")]
        [SwaggerOperation(Summary = "Updates the scope of application for a price book", Description = "Updates the scope of application for a price book.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Price book scope of application updated successfully")]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> UpdateScopeOfApplication([FromBody] UpdateScopeOfApplicationRequest request)
        {
            var useCase = HttpContext.RequestServices.GetRequiredService<UpdateScopeOfApplicationUseCase>();
            var result = await useCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Updates the custom time for a price book.
        /// </summary>
        /// <param name="request">The request object containing the details of the price book to update.</param>
        /// <returns>A response indicating the result of the update operation.</returns>
        /// <response code="200">Returns the updated price book.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPut("custom-time")]
        [SwaggerOperation(Summary = "Updates the custom time for a price book", Description = "Updates the custom time for a price book.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Price book custom time updated successfully")]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> UpdateCustomTime([FromBody] UpdateCustomTimeRequest request)
        {
            var useCase = HttpContext.RequestServices.GetRequiredService<UpdateCustomTimeUseCase>();
            var result = await useCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Updates all aspects of a price book including basic info, scope of application, product prices, and custom time.
        /// </summary>
        /// <param name="request">The request object containing all aspects to update.</param>
        /// <returns>A response indicating the success of the update operations.</returns>
        /// <response code="200">Returns when all requested updates are processed successfully.</response>
        /// <response code="400">If any of the update requests are invalid.</response>
        [HttpPut]
        [SwaggerOperation(Summary = "Updates all aspects of a price book", Description = "Updates any combination of basic info, scope of application, product prices, and custom time for a price book.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Price book updated successfully")]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If any of the update requests are invalid")]
        public async Task<IActionResult> UpdatePriceBook([FromBody] UpdatePriceBookRequest request)
        {
            var response = new UpdatePriceBookResponse();
            var hasErrors = false;

            // Update basic info if provided
            if (request.BasicInfo != null)
            {
                hasErrors |= await ProcessBasicInfoUpdate(request.BasicInfo, response);
            }

            // Update scope of application if provided
            if (request.ScopeOfApplication != null)
            {
                hasErrors |= await ProcessScopeOfApplicationUpdate(request.ScopeOfApplication, response);
            }

            // Update product prices if provided
            if (request.ProductPrices != null && request.ProductPrices.Count > 0)
            {
                hasErrors |= await ProcessProductPricesUpdate(request.ProductPrices, response);
            }

            // Update custom time if provided
            if (request.CustomTime != null)
            {
                hasErrors |= await ProcessCustomTimeUpdate(request.CustomTime, response);
            }

            if (hasErrors)
            {
                return BadRequest(response);
            }

            return Ok(response);
        }

        private async Task<bool> ProcessBasicInfoUpdate(UpdateBasicInfoRequest basicInfo, UpdatePriceBookResponse response)
        {
            var useCase = HttpContext.RequestServices.GetRequiredService<UpdateBasicInfoUseCase>();
            var result = await useCase.ExecuteAsync(basicInfo);
            response.BasicInfoResult = result.IsSuccess ? result.Value : null;
            
            return AddErrorsToResponse(result, response);
        }

        private async Task<bool> ProcessScopeOfApplicationUpdate(UpdateScopeOfApplicationRequest scopeOfApplication, UpdatePriceBookResponse response)
        {
            var useCase = HttpContext.RequestServices.GetRequiredService<UpdateScopeOfApplicationUseCase>();
            var result = await useCase.ExecuteAsync(scopeOfApplication);
            response.ScopeOfApplicationResult = result.IsSuccess ? result.Value : null;
            
            return AddErrorsToResponse(result, response);
        }

        private async Task<bool> ProcessProductPricesUpdate(List<UpdateProductPriceRequest> productPrices, UpdatePriceBookResponse response)
        {
            var useCase = HttpContext.RequestServices.GetRequiredService<UpdateProductPriceUseCase>();
            var hasErrors = false;

            foreach (var priceRequest in productPrices)
            {
                var result = await useCase.ExecuteAsync(priceRequest);
                if (result.IsSuccess && result.Value != null)
                {
                    response.ProductPriceResults.Add(result.Value);
                }
                
                hasErrors |= AddErrorsToResponse(result, response);
            }

            return hasErrors;
        }

        private async Task<bool> ProcessCustomTimeUpdate(UpdateCustomTimeRequest customTime, UpdatePriceBookResponse response)
        {
            var useCase = HttpContext.RequestServices.GetRequiredService<UpdateCustomTimeUseCase>();
            var result = await useCase.ExecuteAsync(customTime);
            response.CustomTimeResult = result.IsSuccess ? result.Value : null;
            
            return AddErrorsToResponse(result, response);
        }

        private static bool AddErrorsToResponse(dynamic result, UpdatePriceBookResponse response)
        {
            if (result.IsSuccess)
                return false;
            
            if (result.ValidationErrors != null)
            {
                response.Errors.AddRange(result.ValidationErrors);
            }
            else if (!string.IsNullOrEmpty(result.ErrorMessage))
            {
                response.Errors.Add(result.ErrorMessage);
            }
            
            return true;
        }
    }

    /// <summary>
    /// Request model for updating all aspects of a price book.
    /// </summary>
    public class UpdatePriceBookRequest
    {
        /// <summary>
        /// Basic information update request.
        /// </summary>
        public UpdateBasicInfoRequest? BasicInfo { get; set; }
        
        /// <summary>
        /// Scope of application update request.
        /// </summary>
        public UpdateScopeOfApplicationRequest? ScopeOfApplication { get; set; }
        
        /// <summary>
        /// List of product price update requests.
        /// </summary>
        public List<UpdateProductPriceRequest>? ProductPrices { get; set; }
        
        /// <summary>
        /// Custom time update request.
        /// </summary>
        public UpdateCustomTimeRequest? CustomTime { get; set; }
    }

    /// <summary>
    /// Response model for the update all aspects operation.
    /// </summary>
    public class UpdatePriceBookResponse
    {
        /// <summary>
        /// Result of basic info update.
        /// </summary>
        public object? BasicInfoResult { get; set; }
        
        /// <summary>
        /// Result of scope of application update.
        /// </summary>
        public object? ScopeOfApplicationResult { get; set; }
        
        /// <summary>
        /// Results of product price updates.
        /// </summary>
        public List<object> ProductPriceResults { get; set; } = [];
        
        /// <summary>
        /// Result of custom time update.
        /// </summary>
        public object? CustomTimeResult { get; set; }
        
        /// <summary>
        /// Collection of errors from all update operations.
        /// </summary>
        public List<string> Errors { get; set; } = [];
    }
} 