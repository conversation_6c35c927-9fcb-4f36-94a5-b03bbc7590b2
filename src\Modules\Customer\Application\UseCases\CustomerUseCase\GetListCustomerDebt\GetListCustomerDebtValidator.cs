using FluentValidation;
using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetListCustomerDebt
{
    /// <summary>
    /// Validator for GetListCustomerDebtRequest
    /// </summary>
    public class GetListCustomerDebtValidator : Validator<GetListCustomerDebtRequest>
    {
        public GetListCustomerDebtValidator()
        {
            // CustomerId must be greater than 0
            RuleFor(x => x.CustomerId)
                .GreaterThan(0, "CustomerId must be a positive number");
            
            // Skip must be non-negative
            RuleFor(x => x.Skip)
                .GreaterThanOrEqual(0, "Skip value must be non-negative");
            
            // Take must be positive and not exceed 100
            RuleFor(x => x.Take)
                .GreaterThan(0, "Take value must be greater than zero")
                .LessThan(101, "Take value cannot exceed 100");
        }
    }
} 