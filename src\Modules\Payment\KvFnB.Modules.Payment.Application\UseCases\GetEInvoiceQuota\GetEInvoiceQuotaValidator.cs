using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;

namespace KvFnB.Modules.Payment.Application.UseCases.GetEInvoiceQuota;

public class GetEInvoiceQuotaValidator : Validator<GetEInvoiceQuotaRequest>
{
    public GetEInvoiceQuotaValidator()
    {
        RuleFor(x => x.TaxCode)
            .NotNullOrEmpty("Tax code is required")
            .GreaterThanMaxLength(50, "Tax code must not exceed 50 characters");
    }
} 