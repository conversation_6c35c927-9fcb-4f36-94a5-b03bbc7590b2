using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace KvFnB.Modules.Menu.Restful.Controllers
{
    [ApiController]
    public class BaseController : ControllerBase
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        protected BaseController(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public static IActionResult Success<TData>(Result<TData> result) where TData : class
        {
#pragma warning disable CS8604 // Possible null reference argument.
            return new OkObjectResult(new SuccessResponse<TData>(result.Value));
#pragma warning restore CS8604 // Possible null reference argument.
        }

        public IActionResult Failure<TData>(Result<TData> result) where TData : class
        {
            var response = new ErrorResponse(result.ErrorMessage ?? "Unknown error", result.ValidationErrors);

            if (_httpContextAccessor.HttpContext?.Items != null)
            {
                _httpContextAccessor.HttpContext.Items["ErrorResponse"] = response;
            }

            return new BadRequestObjectResult(response);
        }
    }
}