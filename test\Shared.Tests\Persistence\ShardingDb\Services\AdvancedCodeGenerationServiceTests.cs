using KvFnB.Core.Abstractions;
using KvFnB.Core.Domain;
using KvFnB.Shared.Persistence.ShardingDb;
using KvFnB.Shared.Persistence.ShardingDb.Entities;
using KvFnB.Shared.Persistence.ShardingDb.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Moq;
using KvFnB.Core.Authentication;
using Xunit.Abstractions;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Storage;
using Testcontainers.MsSql;

namespace KvFnB.Shared.Tests.Persistence.ShardingDb.Services
{
    [Collection("Sequential")]
    [Trait("Type", "Integration")]
    public class AdvancedCodeGenerationServiceTests : IClassFixture<MsSqlContainerFixtureProvider>, IAsyncLifetime
    {
        private readonly ITestOutputHelper _output;
        private readonly MsSqlContainerFixtureProvider _fixture;
        private readonly TestShardingDbContext _context;
        private readonly AdvancedCodeGenerationService _service;
        private readonly Mock<ITenantProvider> _tenantProvider;
        private readonly Mock<IAuthUser> _authUser;
        private readonly Mock<ILogger> _logger;
        private const int TenantId = 1;
        private string DbConnectionString => _fixture.ConnectionString;
        private readonly MsSqlContainer _container;

        public AdvancedCodeGenerationServiceTests(MsSqlContainerFixtureProvider fixture, ITestOutputHelper output)
        {
            _fixture = fixture;
            _output = output;
            _tenantProvider = new Mock<ITenantProvider>();
            _authUser = new Mock<IAuthUser>();
            _logger = new Mock<ILogger>();

            // Configure mock objects
            _tenantProvider.Setup(t => t.GetTenantId()).Returns(1);
            _authUser.SetupGet(a => a.TenantId).Returns(1);

            // Ensure DbContextOptions is not null before creating context
            if (_fixture.DbContextOptions == null)
            {
                throw new InvalidOperationException("DbContextOptions is null. Fixture initialization may have failed.");
            }

            _context = new TestShardingDbContext(_tenantProvider.Object, _authUser.Object, _fixture.DbContextOptions);
            _service = new AdvancedCodeGenerationService(_context, _tenantProvider.Object, _logger.Object);
            _container = _fixture.MsSqlContainer;
        }

        public async Task InitializeAsync()
        {
            // Create the database and tables at the start of each test
            await EnsureDatabaseCreated();
            // Clean any existing data to start fresh
            await ClearExistingDataAsync();
        }

        public async Task DisposeAsync()
        {
            await _context.DisposeAsync();
        }

        // This method should be called in each test after setting up the database
        private async Task ClearExistingDataAsync()
        {
            try
            {
                var connection = new SqlConnection(DbConnectionString);
                await connection.OpenAsync();

                // Check if TestEntities table exists before attempting to delete from it
                var tableCheckCommand = new SqlCommand(
                    "IF EXISTS (SELECT * FROM sys.tables WHERE name = 'TestEntities') " +
                    "DELETE FROM TestEntities",
                    connection);

                await tableCheckCommand.ExecuteNonQueryAsync();

                // Check if AutoGeneratedCode table exists before attempting to delete from it
                var codeTableCheckCommand = new SqlCommand(
                    "IF EXISTS (SELECT * FROM sys.tables WHERE name = 'AutoGeneratedCode') " +
                    "DELETE FROM AutoGeneratedCode",
                    connection);

                await codeTableCheckCommand.ExecuteNonQueryAsync();

                await connection.CloseAsync();

                // Also clear the context cache to avoid stale entities
                _context.ChangeTracker.Clear();
            }
            catch (Exception ex)
            {
                _output?.WriteLine($"Error clearing data: {ex}");
                throw;
            }
        }

        // Ensure database and tables are created
        private async Task EnsureDatabaseCreated()
        {
            try
            {
                var connection = new SqlConnection(DbConnectionString);
                await connection.OpenAsync();
                using var command = connection.CreateCommand();

                command.CommandText = "IF OBJECT_ID('TestEntities', 'U') IS NULL CREATE TABLE TestEntities (Id INT IDENTITY(1,1) PRIMARY KEY, RetailerId INT NOT NULL, Code NVARCHAR(50) NOT NULL, Name NVARCHAR(100) NULL, IsDeleted BIT NOT NULL DEFAULT 0)";
                await command.ExecuteNonQueryAsync();

                command.CommandText = @"
                    IF OBJECT_ID('AutoGeneratedCode', 'U') IS NULL 
                    CREATE TABLE AutoGeneratedCode (
                        Id INT IDENTITY(1,1) PRIMARY KEY,
                        RetailerId INT NOT NULL, 
                        TypeOf NVARCHAR(64) NOT NULL, 
                        Prefix NVARCHAR(64) NOT NULL, 
                        Value BIGINT NOT NULL,
                        RowVersion ROWVERSION NOT NULL
                    )";
                await command.ExecuteNonQueryAsync();

                await connection.CloseAsync();
            }
            catch (Exception ex)
            {
                _output?.WriteLine($"Error creating database schema: {ex}");
                throw;
            }
        }

        [Fact]
        public async Task GenerateCodeAsync_WithDefaultParams_ShouldGenerateSequentialCode()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            const string entityType = "TestEntity";
            const string prefix = "TE";
            const string expectedCode = "TE000001"; // First code with default padding

            // Act
            var result = await _service.GenerateCodeAsync(TenantId, entityType, prefix);

            // Assert
            Assert.Equal(expectedCode, result);

            // Verify the sequence was created and initialized
            var sequence = await _context.Set<AutoGeneratedCode>()
                .FirstOrDefaultAsync(c => c.TenantId == TenantId && c.TypeOf == entityType && c.Prefix == prefix);

            Assert.NotNull(sequence);
            Assert.Equal(1, sequence.Value);
        }

        [Fact]
        public async Task GenerateCodeAsync_WithCustomCode_ShouldUpdateEntityCodeAndSequence()
        {
            // Arrange
            await EnsureDatabaseCreated();

            // Clear any existing data
            await _context.Database.ExecuteSqlRawAsync("DELETE FROM TestEntities");
            await _context.Database.ExecuteSqlRawAsync("DELETE FROM AutoGeneratedCode");

            // Insert a sequence with value 100
            await _context.Set<AutoGeneratedCode>().AddAsync(new AutoGeneratedCode
            {
                TenantId = 1,
                TypeOf = "TestEntity",
                Prefix = "TE",
                Value = 100
            });
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GenerateCodeAsync(1, "TestEntity", "TE", "000101");

            // Assert
            Assert.Equal("TE000101", result);

            // Verify sequence value is updated to include the custom value
            var sequence = await _context.Set<AutoGeneratedCode>()
                .FirstOrDefaultAsync(x => x.TenantId == 1 && x.TypeOf == "TestEntity" && x.Prefix == "TE");

            Assert.NotNull(sequence);
            Assert.Equal(101, sequence.Value);
        }

        [Fact]
        public async Task GenerateCodeAsync_WithDuplicateCode_ShouldHandleDuplicateCode()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            var tenantId = TenantId;
            var entityType = "TestEntity";
            var prefix = "TE";
            var firstExpectedCode = "TE000001";

            // Act - Generate first code
            var firstCode = await _service.GenerateCodeAsync(tenantId, entityType, prefix);
            Assert.Equal(firstExpectedCode, firstCode);

            // Create an entity with the generated code
            var entity = new TestEntity
            {
                TenantId = tenantId,
                Code = firstCode,
                Name = "Test Entity"
            };
            _context.TestEntities.Add(entity);
            await _context.SaveChangesAsync();

            // Try to generate duplicate code 
            var duplicateCode = await _service.GenerateCodeAsync(tenantId, entityType, prefix, firstExpectedCode);

            // Assert - Should get a new sequential code, not a duplicate with suffix
            Assert.Equal("TE000002", duplicateCode);

            // Create another entity with the first duplicate code
            var entityWithDuplicate = new TestEntity
            {
                TenantId = tenantId,
                Code = duplicateCode,
                Name = "Test Entity with duplicate code"
            };
            _context.TestEntities.Add(entityWithDuplicate);
            await _context.SaveChangesAsync();

            // Try with another duplicate
            var anotherDuplicate = await _service.GenerateCodeAsync(tenantId, entityType, prefix, firstExpectedCode);

            // Should generate the next sequential code
            Assert.Equal("TE000003", anotherDuplicate);

            // Verify sequence was updated
            var sequence = await _context.Set<AutoGeneratedCode>()
                .FirstOrDefaultAsync(c => c.TenantId == tenantId && c.TypeOf == entityType && c.Prefix == prefix);

            Assert.NotNull(sequence);
            Assert.Equal(3, sequence.Value);
        }

        [Fact]
        public async Task GenerateBatchCodesAsync_ShouldGenerateMultipleSequentialCodes()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            const string entityType = "BatchEntity";
            const string prefix = "BA";
            const int initialValue = 0;
            const int batchSize = 5;
            const int minPadding = 6;

            await using var context = new TestShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _fixture.DbContextOptions);

            // Create service
            var service = new AdvancedCodeGenerationService(
                context,
                _tenantProvider.Object,
                _logger.Object);

            // Act
            var codes = await service.GenerateBatchCodesAsync(
                TenantId,
                entityType,
                prefix,
                batchSize,
                minPadding
            );

            // Assert
            Assert.NotNull(codes);
            Assert.Equal(batchSize, codes.Count);

            // Check expected sequence: BA000001, BA000002, BA000003, BA000004, BA000005
            for (int i = 0; i < batchSize; i++)
            {
                var expectedCode = $"{prefix}{(i + 1).ToString($"D{minPadding}")}";
                Assert.Equal(expectedCode, codes[i]);
            }

            // Check if sequence was created and incremented by batch size
            var sequence = await context.Set<AutoGeneratedCode>().FirstOrDefaultAsync(x =>
                x.TenantId == TenantId && x.TypeOf == entityType && x.Prefix == prefix);
            Assert.NotNull(sequence);
            Assert.Equal(initialValue + batchSize, sequence.Value);
        }

        [Fact]
        public async Task GenerateCodeAndCreateEntityAsync_ShouldGenerateCodeAndCreateEntityAtomically()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            // Create a test context that won't use transactions in GenerateCodeAsync
            var testContext = new TestShardingDbContext(_tenantProvider.Object, _authUser.Object, _fixture.DbContextOptions)
            {
                UseTransactions = false
            };

            // Create a service with the modified context
            var service = new AdvancedCodeGenerationService(testContext, _tenantProvider.Object, _logger.Object);

            // Act - This should use a single transaction for the entire operation
            var result = await service.GenerateCodeAndCreateEntityAsync<TestEntity>(
                1,
                "TestEntity",
                "ATO",
                code => Task.FromResult(new TestEntity
                {
                    TenantId = 1,
                    Name = "Test Entity"
                }));

            // Assert
            Assert.Equal("ATO000001", result.Code);

            // Verify the entity was created and the sequence was updated
            var entity = await testContext.Set<TestEntity>().FirstOrDefaultAsync(e => e.Code == "ATO000001");
            Assert.NotNull(entity);

            var sequence = await testContext.Set<AutoGeneratedCode>()
                .FirstOrDefaultAsync(s => s.TenantId == 1 && s.TypeOf == "TestEntity" && s.Prefix == "ATO");
            Assert.NotNull(sequence);
            Assert.Equal(1, sequence.Value);
        }

        [Fact]
        public async Task UpdateEntityCodeAsync_WhenEntityExists_ShouldUpdateEntityCodeAndReturnNewCode()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            var tenantId = 1;
            var entityType = "TestEntity";
            var prefix = "UPD";
            var oldCode = "UPD000001";
            var newCode = "UPD000005"; // Skip to code 5
            var expectedNextCode = "UPD000006"; // Next code should be 6

            using var context = new TestShardingDbContext(_tenantProvider.Object, _authUser.Object, _fixture.DbContextOptions);

            // Avoid transaction conflicts
            context.Database.AutoTransactionsEnabled = false;

            // Create test entity with initial code
            var testEntity = new TestEntity
            {
                TenantId = tenantId,
                Code = oldCode,
                Name = "Test Entity"
            };
            context.TestEntities.Add(testEntity);
            await context.SaveChangesAsync();
            var entityId = testEntity.Id;
            context.ChangeTracker.Clear();

            var service = new AdvancedCodeGenerationService(context, _tenantProvider.Object, _logger.Object);

            // Create a simple update function
            var result = await service.UpdateEntityCodeAsync<TestEntity>(
                tenantId,
                entityType,
                prefix,
                newCode,
                async code =>
                {
                    var entity = await context.TestEntities.FindAsync(entityId);
                    entity.Code = code;
                    await context.SaveChangesAsync();
                    return entity;
                }
            );

            // Assert
            Assert.Equal(newCode, result.Code);

            // Verify entity was updated in db
            context.ChangeTracker.Clear();
            var updatedEntity = await context.TestEntities.FindAsync(entityId);
            Assert.NotNull(updatedEntity);
            Assert.Equal(newCode, updatedEntity.Code);

            // Verify sequence was updated for future codes
            var nextCode = await service.GenerateCodeAsync(tenantId, entityType, prefix);
            Assert.Equal(expectedNextCode, nextCode);
        }

        [Fact]
        public async Task UpdateEntityCodeAsync_ShouldUpdateCodeAndSequenceAtomically()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            var tenantId = 1;
            var entityType = "TestEntity";
            var prefix = "TE";
            var oldCode = "TE000001";
            var newCode = "TE000099";
            var expectedNextCode = "TE000100";

            using var context = new TestShardingDbContext(_tenantProvider.Object, _authUser.Object, _fixture.DbContextOptions);
            // Avoid transaction conflicts
            context.Database.AutoTransactionsEnabled = false;

            // Create test entity with initial code
            var testEntity = new TestEntity
            {
                TenantId = tenantId,
                Code = oldCode,
                Name = "Test Entity"
            };
            context.TestEntities.Add(testEntity);
            await context.SaveChangesAsync();
            var entityId = testEntity.Id;
            context.ChangeTracker.Clear();

            var service = new AdvancedCodeGenerationService(context, _tenantProvider.Object, _logger.Object);

            // Act - Update the code
            var result = await service.UpdateEntityCodeAsync<TestEntity>(
                tenantId,
                entityType,
                prefix,
                newCode,
                async code =>
                {
                    var entity = await context.TestEntities.FindAsync(entityId);
                    entity.Code = code;
                    await context.SaveChangesAsync();
                    return entity;
                }
            );

            // Assert
            Assert.Equal(newCode, result.Code);

            // Verify entity was updated in db
            context.ChangeTracker.Clear();
            var updatedEntity = await context.TestEntities.FindAsync(entityId);
            Assert.NotNull(updatedEntity);
            Assert.Equal(newCode, updatedEntity.Code);

            // Verify sequence was updated for future codes
            var nextCode = await service.GenerateCodeAsync(tenantId, entityType, prefix);
            Assert.Equal(expectedNextCode, nextCode);
        }

        [Fact]
        public async Task UpdateEntityCodeAsync_WithDuplicateCode_ShouldThrowException()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            using var connection = new SqlConnection(_fixture.ConnectionString);
            await connection.OpenAsync();
            using var command = connection.CreateCommand();

            command.CommandText = "INSERT INTO TestEntities (RetailerId, Code, Name) VALUES (1, 'DUP001', 'Existing Entity')";
            await command.ExecuteNonQueryAsync();
            await connection.CloseAsync();

            using var context = new TestShardingDbContext(_tenantProvider.Object, _authUser.Object, _fixture.DbContextOptions);
            var service = new AdvancedCodeGenerationService(context, _tenantProvider.Object, _logger.Object);

            // Create another entity with a different code
            var entity = new TestEntity { TenantId = 1, Code = "ORIG001", Name = "New Entity" };
            context.TestEntities.Add(entity);
            await context.SaveChangesAsync();
            var entityId = entity.Id;
            context.ChangeTracker.Clear();

            // Act & Assert - Try to update to a duplicate code should throw
            await Assert.ThrowsAsync<InvalidOperationException>(async () =>
            {
                await service.UpdateEntityCodeAsync<TestEntity>(
                    1,
                    "TestEntity",
                    "DUP",
                    "DUP001",
                    async code =>
                    {
                        var entityToUpdate = await context.TestEntities.FindAsync(entityId);
                        entityToUpdate.Code = code;
                        await context.SaveChangesAsync();
                        return entityToUpdate;
                    }
                );
            });
        }

        [Fact]
        public async Task GenerateCodeAsync_WithCustomPadding_ShouldPadCodeCorrectly()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            const string entityType = "PaddingEntity";
            const string prefix = "PD";
            const int customPadding = 8; // Larger padding than default
            const string expectedCode = "PD00000001"; // 8 digits

            await using var context = new TestShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _fixture.DbContextOptions);

            // Create service
            var service = new AdvancedCodeGenerationService(
                context,
                _tenantProvider.Object,
                _logger.Object);

            // Act
            var result = await service.GenerateCodeAsync(
                TenantId,
                entityType,
                prefix,
                null, // No custom code
                customPadding
            );

            // Assert
            Assert.Equal(expectedCode, result);
            Assert.Equal(customPadding + prefix.Length, result.Length);

            // Check if sequence was created with correct value
            var sequence = await context.Set<AutoGeneratedCode>().FirstOrDefaultAsync(x =>
                x.TenantId == TenantId && x.TypeOf == entityType && x.Prefix == prefix);
            Assert.NotNull(sequence);
            Assert.Equal(1, sequence.Value);
        }

        [Fact]
        public async Task GenerateCodeAsync_WithInvalidParameters_ShouldThrowArgumentException()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            await using var context = new TestShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _fixture.DbContextOptions);

            var service = new AdvancedCodeGenerationService(
                context,
                _tenantProvider.Object,
                _logger.Object);

            // Act & Assert - Invalid tenant ID
            await Assert.ThrowsAsync<ArgumentException>(async () =>
                await service.GenerateCodeAsync(0, "Product", "PRD"));

            // Act & Assert - Empty type
            await Assert.ThrowsAsync<ArgumentException>(async () =>
                await service.GenerateCodeAsync(1, "", "PRD"));

            // Act & Assert - Empty prefix
            await Assert.ThrowsAsync<ArgumentException>(async () =>
                await service.GenerateCodeAsync(1, "Product", ""));

            // Act & Assert - Invalid padding
            await Assert.ThrowsAsync<ArgumentException>(async () =>
                await service.GenerateCodeAsync(1, "Product", "PRD", null, 0));
        }

        [Fact]
        public async Task GenerateCodeAsync_WithDifferentTenantId_ShouldThrowUnauthorizedAccessException()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            const int differentTenantId = 2; // Different from the one in the tenant provider

            await using var context = new TestShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _fixture.DbContextOptions);

            // Setup tenant provider to return a specific tenant ID
            _tenantProvider.Setup(tp => tp.GetTenantId()).Returns(1);

            var service = new AdvancedCodeGenerationService(
                context,
                _tenantProvider.Object,
                _logger.Object);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
                await service.GenerateCodeAsync(differentTenantId, "Product", "PRD"));

            Assert.Contains("Cannot generate code for tenant", exception.Message);
        }

        [Fact]
        public async Task GenerateCodeAsync_WithNumericOverflow_ShouldHandleExpansion()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            const string entityType = "OverflowEntity";
            const string prefix = "OV";
            const int minPadding = 2; // Small padding to test overflow

            await using var context = new TestShardingDbContext(new MockTenantProvider(), new MockAuthUser(), _fixture.DbContextOptions);

            // Create an initial code sequence with a value near overflow
            var codeEntity = new AutoGeneratedCode
            {
                TenantId = TenantId,
                TypeOf = entityType,
                Prefix = prefix,
                Value = 99 // Next value will be 100, which exceeds 2-digit padding
            };

            context.Set<AutoGeneratedCode>().Add(codeEntity);
            await context.SaveChangesAsync();

            var service = new AdvancedCodeGenerationService(
                context,
                _tenantProvider.Object,
                _logger.Object);

            // Act - Should handle properly when exceeding the padding
            var result = await service.GenerateCodeAsync(
                TenantId,
                entityType,
                prefix,
                null,
                minPadding
            );

            // Assert
            Assert.Equal("OV100", result); // Should expand beyond padding

            // Check if sequence was incremented correctly
            var sequence = await context.Set<AutoGeneratedCode>().FirstOrDefaultAsync(x =>
                x.TenantId == TenantId && x.TypeOf == entityType && x.Prefix == prefix);
            Assert.NotNull(sequence);
            Assert.Equal(100, sequence.Value);
        }

        [Fact]
        public async Task GenerateCodeAsync_WithExistingLegacyData_ShouldInitializeSequenceCorrectly()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            // Insert pre-existing data with codes
            using var connection = new SqlConnection(_fixture.ConnectionString);
            await connection.OpenAsync();
            using var command = connection.CreateCommand();

            command.CommandText = "INSERT INTO TestEntities (RetailerId, Code, Name) VALUES (1, 'LEG000001', 'Entity 1')";
            await command.ExecuteNonQueryAsync();

            command.CommandText = "INSERT INTO TestEntities (RetailerId, Code, Name) VALUES (1, 'LEG000002', 'Entity 2')";
            await command.ExecuteNonQueryAsync();

            command.CommandText = "INSERT INTO TestEntities (RetailerId, Code, Name) VALUES (1, 'LEG000005', 'Entity 5')";
            await command.ExecuteNonQueryAsync();

            await connection.CloseAsync();

            using var context = new TestShardingDbContext(_tenantProvider.Object, _authUser.Object, _fixture.DbContextOptions);
            var service = new AdvancedCodeGenerationService(context, _tenantProvider.Object, _logger.Object);

            // Act
            var result = await service.GenerateCodeAsync(1, "TestEntity", "LEG");

            // Assert - Should find highest existing code and continue from there
            Assert.Equal("LEG000006", result);
        }

        [Fact]
        public async Task GenerateCodeAsync_WithMixedFormatLegacyData_ShouldFindHighestValidNumericPart()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            // Insert pre-existing data with mixed format codes
            using var connection = new SqlConnection(_fixture.ConnectionString);
            await connection.OpenAsync();
            using var command = connection.CreateCommand();

            command.CommandText = "INSERT INTO TestEntities (RetailerId, Code, Name) VALUES (1, 'MIX001', 'Entity 1')";  // Old format
            await command.ExecuteNonQueryAsync();

            command.CommandText = "INSERT INTO TestEntities (RetailerId, Code, Name) VALUES (1, 'MIX000002', 'Entity 2')";  // New format
            await command.ExecuteNonQueryAsync();

            command.CommandText = "INSERT INTO TestEntities (RetailerId, Code, Name) VALUES (1, 'MIX-3', 'Entity 3')";  // Another format
            await command.ExecuteNonQueryAsync();

            command.CommandText = "INSERT INTO TestEntities (RetailerId, Code, Name) VALUES (1, 'MIXABC', 'Entity ABC')";  // Non-numeric
            await command.ExecuteNonQueryAsync();

            await connection.CloseAsync();

            using var context = new TestShardingDbContext(_tenantProvider.Object, _authUser.Object, _fixture.DbContextOptions);
            var service = new AdvancedCodeGenerationService(context, _tenantProvider.Object, _logger.Object);

            // Act
            var result = await service.GenerateCodeAsync(1, "TestEntity", "MIX");

            // Assert - Should find highest valid numeric part (3) and continue from there
            Assert.Equal("MIX000003", result);

            // Verify the sequence was initialized correctly
            var sequence = await context.Set<AutoGeneratedCode>().FirstOrDefaultAsync(x =>
                x.TenantId == TenantId && x.TypeOf == "TestEntity" && x.Prefix == "MIX");
            Assert.NotNull(sequence);
            Assert.Equal(3, sequence.Value);
        }

        [Fact]
        public async Task GenerateDeletionCodeAsync_FirstDeletion_ShouldAppendDELSuffix()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            const string originalCode = "PRD000001";
            const string expectedCode = "PRD000001{DEL}";

            // Create test entity
            var entity = new TestEntity
            {
                TenantId = TenantId,
                Code = originalCode,
                Name = "Test Product"
            };
            _context.TestEntities.Add(entity);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GenerateDeletionCodeAsync<TestEntity>(
                TenantId,
                originalCode,
                "TestEntity");

            // Assert
            Assert.Equal(expectedCode, result);
        }

        [Fact]
        public async Task GenerateDeletionCodeAsync_SubsequentDeletions_ShouldIncrementDELSuffix()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            const string originalCode = "PRD000001";

            // Create test entities with deletion codes
            var entities = new[]
            {
                new TestEntity { TenantId = TenantId, Code = $"{originalCode}{{DEL}}", Name = "First Deletion", IsDeleted = true },
                new TestEntity { TenantId = TenantId, Code = $"{originalCode}{{DEL1}}", Name = "Second Deletion", IsDeleted = true }
            };

            _context.TestEntities.AddRange(entities);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GenerateDeletionCodeAsync<TestEntity>(
                TenantId,
                originalCode,
                "TestEntity");

            // Assert
            Assert.Equal($"{originalCode}{{DEL2}}", result);
        }

        [Fact]
        public async Task GenerateDeletionCodeAsync_WithEmptyCode_ShouldThrowArgumentException()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(async () =>
                await _service.GenerateDeletionCodeAsync<TestEntity>(
                    TenantId,
                    "",
                    "TestEntity"));
        }

        [Fact]
        public async Task GenerateDeletionCodeAsync_WithDifferentTenant_ShouldNotCountOtherTenantDeletions()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            const string originalCode = "PRD000001";
            const int otherTenantId = 2;

            // Create deleted entities for different tenant
            var otherTenantEntities = new[]
            {
                new TestEntity { TenantId = otherTenantId, Code = $"{originalCode}{{DEL}}", Name = "Other Tenant First" },
                new TestEntity { TenantId = otherTenantId, Code = $"{originalCode}{{DEL1}}", Name = "Other Tenant Second" }
            };

            _context.TestEntities.AddRange(otherTenantEntities);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GenerateDeletionCodeAsync<TestEntity>(
                TenantId,
                originalCode,
                "TestEntity");

            // Assert - Should get first deletion code since other tenant's deletions don't count
            Assert.Equal($"{originalCode}{{DEL}}", result);
        }

        [Fact]
        public async Task GenerateDeletionCodeAsync_WithGaps_ShouldUseNextAvailableNumber()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            const string originalCode = "PRD000001";

            // Create test entities with non-sequential deletion codes
            var entities = new[]
            {
                new TestEntity { TenantId = TenantId, Code = $"{originalCode}{{DEL}}", Name = "First Deletion", IsDeleted = true },
                new TestEntity { TenantId = TenantId, Code = $"{originalCode}{{DEL2}}", Name = "Third Deletion", IsDeleted = true }
            };

            _context.TestEntities.AddRange(entities);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GenerateDeletionCodeAsync<TestEntity>(
                TenantId,
                originalCode,
                "TestEntity");

            // Assert - Should use 3 since 0 (DEL) and 2 are taken
            Assert.Equal($"{originalCode}{{DEL3}}", result);
        }

        [Fact]
        public async Task GenerateDeletionCodeAsync_WithInvalidTenant_ShouldThrowUnauthorizedAccessException()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            const int invalidTenantId = 999;
            const string originalCode = "PRD000001";

            // Act & Assert
            await Assert.ThrowsAsync<UnauthorizedAccessException>(async () =>
                await _service.GenerateDeletionCodeAsync<TestEntity>(
                    invalidTenantId,
                    originalCode,
                    "TestEntity"));
        }

        // [Fact]
        // public async Task GenerateCodeAsync_WithConcurrencyConflict_ShouldRetryAndSucceed()
        // {
        //     // Arrange
        //     await EnsureDatabaseCreated();
        //     await ClearExistingDataAsync();

        //     using var connection = new SqlConnection(_fixture.ConnectionString);
        //     await connection.OpenAsync();
        //     using var command = connection.CreateCommand();

        //     // Make sure the table schema is setup correctly
        //     command.CommandText = @"
        //         IF EXISTS (SELECT * FROM sys.tables WHERE name = 'AutoGeneratedCode')
        //             DROP TABLE AutoGeneratedCode;

        //         CREATE TABLE AutoGeneratedCode (
        //             Id INT IDENTITY(1,1) PRIMARY KEY,
        //             RetailerId INT NOT NULL,
        //             TypeOf NVARCHAR(64) NOT NULL,
        //             Prefix NVARCHAR(64) NOT NULL,
        //             Value BIGINT NOT NULL,
        //             RowVersion ROWVERSION NOT NULL
        //         );
        //     ";
        //     await command.ExecuteNonQueryAsync();

        //     // Create a special context that will throw concurrency exceptions initially 
        //     using var specialContext = new TestShardingDbContextWithConcurrencyConflict(
        //         _tenantProvider.Object, 
        //         _authUser.Object,
        //         _fixture.DbContextOptions);

        //     var service = new AdvancedCodeGenerationService(specialContext, _tenantProvider.Object, _logger.Object);

        //     // Act
        //     var result = await service.GenerateCodeAsync(1, "TestEntity", "CCT");

        //     // Assert
        //     Assert.Equal("CCT000001", result);
        //     Assert.True(specialContext.SaveChangesCalls > 1, $"Expected multiple save attempts, but got {specialContext.SaveChangesCalls}");
        // }

        // Special test DbContext that simulates a concurrency exception
        private class TestShardingDbContextWithConcurrencyConflict : TestShardingDbContext
        {
            public int SaveChangesCalls { get; private set; } = 0;
            private bool _alreadyThrown = false;

            public TestShardingDbContextWithConcurrencyConflict(
                ITenantProvider tenantProvider,
                IAuthUser authUser,
                DbContextOptions<ShardingDbContext> options)
                : base(tenantProvider, authUser, options)
            {
            }

            public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
            {
                SaveChangesCalls++;

                // On first save, throw a concurrency exception to simulate conflict
                if (!_alreadyThrown)
                {
                    _alreadyThrown = true;

                    // Update the entities to simulate a concurrent update
                    var entries = ChangeTracker.Entries<AutoGeneratedCode>().ToList();
                    foreach (var entry in entries)
                    {
                        if (entry.Entity is AutoGeneratedCode agc)
                        {
                            // Simulate another process incrementing before our retry
                            agc.Value += 1;
                        }
                    }

                    // Use the constructor that takes just a message
                    throw new DbUpdateConcurrencyException("Simulated concurrency conflict");
                }

                return await base.SaveChangesAsync(cancellationToken);
            }
        }

        private class TestDatabaseFacade : DatabaseFacade
        {
            private readonly Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction _mockTransaction;

            public TestDatabaseFacade(
                DbContext context,
                Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction mockTransaction)
                : base(context)
            {
                _mockTransaction = mockTransaction;
            }

            public override Task<Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction> BeginTransactionAsync(
                CancellationToken cancellationToken = default)
            {
                return Task.FromResult(_mockTransaction);
            }
        }

        [Fact]
        public async Task GenerateCodeAndCreateEntityAsync_WhenEntityCreationFails_ShouldRollbackTransaction()
        {
            // Arrange
            await EnsureDatabaseCreated();
            await ClearExistingDataAsync();

            // First create a regular context and service to initialize sequence
            var initialContext = new TestShardingDbContext(_tenantProvider.Object, _authUser.Object, _fixture.DbContextOptions);
            var initialService = new AdvancedCodeGenerationService(initialContext, _tenantProvider.Object, _logger.Object);

            // Manually insert a code entry to track
            var codeEntity = new AutoGeneratedCode
            {
                TenantId = 1,
                TypeOf = "TestEntity",
                Prefix = "FAIL",
                Value = 5 // Initial value
            };
            initialContext.Set<AutoGeneratedCode>().Add(codeEntity);
            await initialContext.SaveChangesAsync();

            // Create context that will throw during SaveChanges
            var failingContext = new TestShardingDbContext(_tenantProvider.Object, _authUser.Object, _fixture.DbContextOptions)
            {
                ThrowOnSave = true // This will cause SaveChanges to throw
            };

            var service = new AdvancedCodeGenerationService(failingContext, _tenantProvider.Object, _logger.Object);

            // Act & Assert
            // Verify that when entity creation fails, we get an exception
            await Assert.ThrowsAsync<Exception>(async () =>
            {
                await service.GenerateCodeAndCreateEntityAsync<TestEntity>(
                    1,
                    "TestEntity",
                    "FAIL",
                    code => Task.FromResult(new TestEntity
                    {
                        TenantId = 1,
                        Code = code,
                        Name = "This entity creation will fail"
                    }));
            });

            // Verify database state - Check that the transaction was rolled back by verifying
            // the AutoGeneratedCode value didn't change from the initial value
            using var verificationContext = new TestShardingDbContext(_tenantProvider.Object, _authUser.Object, _fixture.DbContextOptions);
            var verificationCodeEntity = await verificationContext.Set<AutoGeneratedCode>()
                .FirstOrDefaultAsync(c => c.TenantId == 1 && c.TypeOf == "TestEntity" && c.Prefix == "FAIL");

            Assert.NotNull(verificationCodeEntity);
            Assert.Equal(5, verificationCodeEntity.Value); // Should still be 5, not incremented

            // Also verify no TestEntity with the code was created
            var entityCheck = await verificationContext.TestEntities
                .FirstOrDefaultAsync(e => e.TenantId == 1 && e.Code.StartsWith("FAIL"));

            Assert.Null(entityCheck); // Should be null as entity creation failed
        }

        // Special context that simulates failures to force transaction rollback
        private class TestFailingContext : TestShardingDbContext
        {
            private readonly Mock<DatabaseFacade> _mockDatabase;

            public TestFailingContext(
                ITenantProvider tenantProvider,
                IAuthUser authUser,
                DbContextOptions<ShardingDbContext>? options,
                IDbContextTransaction mockTransaction)
                : base(tenantProvider, authUser, options)
            {
                // Create a mock DatabaseFacade that returns our mock transaction
                _mockDatabase = new Mock<DatabaseFacade>(this);
                _mockDatabase
                    .Setup(d => d.BeginTransactionAsync(It.IsAny<CancellationToken>()))
                    .ReturnsAsync(mockTransaction);
            }

            public override DatabaseFacade Database => _mockDatabase.Object;

            public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
            {
                throw new ApplicationException("Simulated failure during entity creation");
            }

            // Removing the problematic BeginTransactionAsync override
        }

        private class TestEntity : ITenantEntity, ICode, ISoftDeletable
        {
            public int Id { get; set; }
            public int TenantId { get; set; }
            public string Code { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
            public bool? IsDeleted { get; set; } = false;

            public void UpdateCode(string code)
            {
                throw new NotImplementedException();
            }
        }

        private class MockTenantProvider : ITenantProvider
        {
            public int? GetTenantId() => TenantId;
            public int? GetShardId() => 1;
            public long? GetBranchId() => null;
            public string? GetRetailerCode() => "KvFnB";

            public void InitTenantInfo(int shard, int tenantId)
            {
                throw new NotImplementedException();
            }
        }

        private class MockAuthUser : IAuthUser
        {
            public long Id { get; set; } = 1;
            public string UserName { get; set; } = "TestUser";
            public long TenantId { get; set; } = 1;
            public string SessionId { get; set; } = "TestSession";
            public bool IsAdmin { get; set; } = false;
        }

        /// <summary>
        /// Custom ShardingDbContext for tests that includes TestEntity in the model
        /// </summary>
        private class TestShardingDbContext : ShardingDbContext
        {
            public DbSet<TestEntity> TestEntities { get; set; } = null!;
            public bool UseTransactions { get; set; } = true;
            public bool ThrowOnSave { get; set; }
            public List<Guid> UpdatedEntitiesIds { get; } = new List<Guid>();

            public TestShardingDbContext(ITenantProvider tenantProvider, IAuthUser authUser, DbContextOptions<ShardingDbContext>? options)
                : base(tenantProvider, authUser, options)
            {
            }

            protected override void OnModelCreating(ModelBuilder modelBuilder)
            {
                base.OnModelCreating(modelBuilder);

                modelBuilder.Entity<TestEntity>(entity =>
                {
                    entity.ToTable("TestEntities");
                    entity.HasKey(e => e.Id);
                    entity.Property(e => e.Id).ValueGeneratedOnAdd();
                    entity.Property(e => e.TenantId).HasColumnName("RetailerId").IsRequired();
                    entity.Property(e => e.Code).IsRequired().HasMaxLength(50);
                    entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                    entity.Property(e => e.IsDeleted).HasColumnName("IsDeleted").HasDefaultValue(false);
                    entity.HasIndex(e => new { e.TenantId, e.Code }).IsUnique();
                });
            }

            public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
            {
                if (ThrowOnSave)
                {
                    throw new Exception("Error saving changes");
                }

                return await base.SaveChangesAsync(cancellationToken);
            }
        }
    }

    [CollectionDefinition("MsSql Container Collection")]
    public class MsSqlContainerCollection : ICollectionFixture<MsSqlContainerFixtureProvider>
    {
    }
} 