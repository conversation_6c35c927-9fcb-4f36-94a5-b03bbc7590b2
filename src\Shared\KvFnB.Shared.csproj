﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\Core\KvFnB.Core.csproj" />
    <ProjectReference Include="..\Localization\KvFnB.Localization.csproj" />
    <ProjectReference Include="..\Modules\DataManagement\Domain\KvFnB.Modules.DataManagement.Domain.csproj" />
    <ProjectReference Include="..\Modules\Menu\Domain\KvFnB.Modules.Menu.Domain.csproj" />
    <ProjectReference Include="..\Modules\Customer\Domain\KvFnB.Modules.Customer.Domain.csproj" />
    <ProjectReference Include="..\Modules\Payment\KvFnB.Modules.Payment.Domain\KvFnB.Modules.Payment.Domain.csproj" />
    <ProjectReference Include="..\Modules\DataManagement\Domain\KvFnB.Modules.DataManagement.Domain.csproj" />
    <ProjectReference Include="..\Modules\Supplier\Domain\KvFnB.Modules.Supplier.Domain\KvFnB.Modules.Supplier.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.S3" Version="3.7.305.28" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.300" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Confluent.Kafka" Version="2.3.0" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="IdentityModel" Version="7.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="KvFnBMoney" Version="1.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.11">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.11" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="MongoDB.Driver" Version="3.1.0" />
    <PackageReference Include="RestSharp" Version="105.2.3" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.4" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.24" />
    <PackageReference Include="Microsoft.AspNetCore.Session" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.HttpsPolicy" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.CookiePolicy" Version="2.3.0" />
    <PackageReference Include="MailKit" Version="4.12.0" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
     <RootNamespace>KvFnB.Shared</RootNamespace>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
