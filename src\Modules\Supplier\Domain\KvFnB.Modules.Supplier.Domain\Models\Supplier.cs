﻿using KvFnB.Core.Domain;
using KvFnB.Modules.Supplier.Domain.Helpers;

namespace KvFnB.Modules.Supplier.Domain.Models
{
    public class Supplier : Entity<int>, IAuditableEntity, ISoftDeletableEntity, ICode
    {
        public const string CodePrefix = "NCC"; //NOSONAR
        public const string CodeDelSuffix = "{DEL"; //NOSONAR
        public const string MobilePhone = "Supplier_MobilePhone";//NOSONAR
        public const string Import = "Supplier_Import";//NOSONAR
        public const string Export = "Supplier_Export";//NOSONAR
        public string Code { get; set; } = string.Empty;
        public string? Name { get; set; }
        public string? Company { get; set; }
        public string? ContactName { get; set; }
        public string? Phone { get; set; }
        public string? Mobile { get; set; }
        public string? Fax { get; set; }
        public string? SearchNumber { get; set; }
        public string? Email { get; set; }
        public string? Website { get; set; }
        public string? Address { get; set; }
        public decimal? Debt { get; set; }
        public string? TaxCode { get; set; }
        public string? Comment { get; set; }
        public string? LocationName { get; set; }
        public string? WardName { get; set; }
        public int? BranchId { get; set; }
        public bool IsActive { get; set; }
        public long? AddressId { get; set; }
        public List<SupplierGroupDetail>? SupplierGroupDetails { get; set; }

        public DateTime CreatedAt { get; set; }
        public long CreatedBy { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public long? ModifiedBy { get; set; }
        public bool? IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public long? DeletedBy { get; set; }

        public Supplier() { }


        public void AssignGroup(List<SupplierGroupDetail> supplierGroupDetails)
        {
            SupplierGroupDetails = supplierGroupDetails;
        }

        public void ChangeSupplierCode(string code)
        {
            Code = code;
        }

        public void UpdateBasicInfo(string name, string company, string phone, string email, string comment)
        {
            Name = name;
            Company = company;
            Phone = phone;
            SearchNumber = !string.IsNullOrEmpty(phone) ? PhoneNumberHelpers.GetPerfectContactNumber(phone) : phone;
            Email = email;
            Comment = comment;
        }

        public void UpdateLocationSupplier(string address, string locationName, string wardName)
        {
            LocationName = locationName;
            WardName = wardName;
            Address = address;
        }

        public void UpdateSupplierActive()
        {
            IsActive = true;
        }

        public void UpdateSupplierDeActive()
        {
            IsActive = false;
        }

        public void UpdateTaxCode(string taxCode)
        {
            TaxCode = taxCode;
        }

        public void UpdateAddressId(long? addressId)
        {
            AddressId = addressId;
        }

        public void UpdateCode(string code)
        {
            Code = code;
        }
    }
}
