using KiotViet.OTP.Abstractions;
using KiotViet.OTP.Configurations;
using KiotViet.OTP.Impls;
using KiotViet.Persistence;
using KvFnB.Core.Abstractions;
using KvFnB.Modules.DataManagement.Domain.Services;
using KvFnB.Modules.DataManagement.Infrastructure.Models;
using KvFnB.Modules.DataManagement.Infrastructure.Services;
using KvFnB.Modules.DataManagement.Infrastructure.Services.Otp;
using KvFnB.Modules.DataManagement.Infrastructure.Services.Otp.Sender;
using KvFnB.Shared.KmaService;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace KvFnB.Modules.DataManagement.Infrastructure.DependencyInjection
{
    /// <summary>
    /// Extension methods for registering DataManagement services
    /// </summary>
    public static class ServiceRegistration
    {
        /// <summary>
        /// Registers DataManagement infrastructure services
        /// </summary>
        public static IServiceCollection AddDataManagementServices(
            this IServiceCollection services)
        {
            // Register services
            services.AddScoped<IBranchService, BranchService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IKmaService, KmaService>();
            services.AddScoped<IOtpService, OtpService>();

            // Register KMA configuration from app settings
            services.AddSingleton(sp =>
            {
                var configuration = sp.GetRequiredService<IConfiguration>();
                var kmaConfigSection = configuration.GetSection("Kma");
                var config = new KmaConfiguration();
                kmaConfigSection.Bind(config);
                return config;
            });

            // Register OTP configuration from app settings
            services.AddSingleton(sp =>
            {
                var configuration = sp.GetRequiredService<IConfiguration>();
                var otpConfigSection = configuration.GetSection("OtpConfig");
                var config = new OtpConfiguration();
                otpConfigSection.Bind(config);
                return config;
            });

            services.AddSingleton<IOtpRedisClients>(sp =>
            {
                var configuration = sp.GetRequiredService<IConfiguration>();
                string redisConnection = configuration.GetValue<string>("TfaRedisMessagequeue") ?? string.Empty;

                // Ensure redisConnection is not null to fix CS8604
                if (string.IsNullOrEmpty(redisConnection))
                {
                    throw new ArgumentNullException(nameof(redisConnection), "Redis connection string cannot be null or empty.");
                }

                // Explicitly cast OtpRedisManagerPool to IOtpRedisClients to fix CS0266
                return new OtpRedisManagerPool(redisConnection);
            });
            // Register OTP Persistent
            services.AddScoped<IKvOTPPersistent>(sp =>
            {
                var redisClientsManager = sp.GetRequiredService<IOtpRedisClients>();
                return new RedisKvOTPPersistent(redisClientsManager);
            });

            // Register OTP Generator
            services.AddScoped<IKvOTPGenerator>(sp =>
            {
                var kvOTPPersistent = sp.GetRequiredService<IKvOTPPersistent>();
                return new KvSMSOTP(kvOTPPersistent);
            });

            // Register OTP Verificator
            services.AddScoped<IKvOTPVerificator>(sp =>
            {
                var kvOTPPersistent = sp.GetRequiredService<IKvOTPPersistent>();
                return new KvSMSOTP(kvOTPPersistent);
            });

            // Register OTP SMS Configuration
            services.AddSingleton(sp =>
            {
                var configuration = sp.GetRequiredService<IConfiguration>();
                var otpMessageTemplate = configuration.GetValue<string>("OtpConfig:OtpMessageTemplate");
                var kvOTPSmSConfiguration = new KvOTPSmSConfiguration();
                kvOTPSmSConfiguration.ConfigMessageTemplate(otpMessageTemplate);
                return kvOTPSmSConfiguration;
            });

            // Register OTP Sender
            services.AddScoped<IKvOTPSender>(sp =>
            {
                var kvOTPSmSConfiguration = sp.GetRequiredService<KvOTPSmSConfiguration>();
                var messageQueue = sp.GetRequiredService<IMessageQueue<SmsMessageRequest>>();
                return new KvOTPSmSSender(kvOTPSmSConfiguration, messageQueue);
            });

            // Register message queue for SMS
            services.AddScoped<IMessageQueue<SmsMessageRequest>>(sp =>
            {
                var kafkaProducer = sp.GetRequiredService<IMessageQueueService>();
                return new SmSMessageQueue<SmsMessageRequest>(kafkaProducer);
            });

            return services;
        }
    }
}