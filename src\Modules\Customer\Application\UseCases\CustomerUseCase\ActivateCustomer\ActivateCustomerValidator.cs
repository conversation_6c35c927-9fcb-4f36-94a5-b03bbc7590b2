using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.ActivateCustomer
{
    /// <summary>
    /// Validator for the ActivateCustomerRequest
    /// </summary>
    public class ActivateCustomerValidator : Validator<ActivateCustomerRequest>
    {
        public ActivateCustomerValidator()
        {
            RuleFor(x => x.CustomerId)
                .NotNull("Customer ID is required")
                .GreaterThan(0, "Customer ID must be greater than zero");
        }
    }
} 