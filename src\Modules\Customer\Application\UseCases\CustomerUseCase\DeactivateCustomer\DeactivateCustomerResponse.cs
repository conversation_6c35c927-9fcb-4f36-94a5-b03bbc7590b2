using System.ComponentModel;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.DeactivateCustomer
{
    /// <summary>
    /// Represents the response model for the DeactivateCustomer use case
    /// </summary>
    public record DeactivateCustomerResponse
    {
        /// <summary>
        /// The unique identifier of the deactivated customer
        /// </summary>
        [JsonPropertyName("customer_id")]
        [Description("The unique identifier of the deactivated customer")]
        public long CustomerId { get; init; }
        
        /// <summary>
        /// Indicates whether the customer was successfully deactivated
        /// </summary>
        [JsonPropertyName("is_deactivated")]
        [Description("Indicates whether the customer was successfully deactivated")]
        public bool IsDeactivated { get; init; }
    }
} 