using KvFnB.Core.Abstractions;
using KvFnB.Core.Validation;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.ActivateCustomer;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.CreateCustomer;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.DeactivateCustomer;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.DeleteCustomer;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetCustomerDetail;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetListCustomer;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.GetListCustomerDebt;
using KvFnB.Modules.Customer.Domain.Repositories;
using KvFnB.Modules.Customer.Infrastructure.Mapping;
using KvFnB.Modules.Customer.Infrastructure.Persistence;
using KvFnB.Shared.AuditTrailMessagePublisher;
using KvFnB.Shared.UseCaseFactory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace KvFnB.Modules.Customer.Infrastructure.DependencyInjection
{
    public static class CustomerModule
    {
        public static IServiceCollection AddCustomerModule(this IServiceCollection services, IConfiguration configuration)
        {
            // Register user cases Customer
            services.AddScoped<GetListCustomerUseCase>();
            services.AddScoped<GetCustomerDetailUseCase>();
            services.AddScoped<DeleteCustomerUseCase>();
            services.AddScoped<CreateCustomerUseCase>();
            services.AddScoped<ActivateCustomerUseCase>();
            services.AddScoped<DeactivateCustomerUseCase>();
			services.AddScoped<GetListCustomerDebtUseCase>();
            
            // Register validators
            services.AddScoped<IValidator<GetListCustomerRequest>, GetListCustomerValidator>();
            services.AddScoped<IValidator<GetListCustomerDebtRequest>, GetListCustomerDebtValidator>();
            services.AddScoped<IValidator<DeleteCustomerRequest>, DeleteCustomerValidator>();
            services.AddScoped<IValidator<CreateCustomerRequest>, CreateCustomerValidator>();
            services.AddScoped<IValidator<ActivateCustomerRequest>, ActivateCustomerValidator>();
            services.AddScoped<IValidator<DeactivateCustomerRequest>, DeactivateCustomerValidator>();

            // Register use case factory
            services.AddScoped<IUseCaseFactory, UseCaseFactory>();

            // Register repositories
            services.AddScoped<ICustomerRepository, CustomerRepository>();
            services.AddScoped<ICustomerGroupRepository, CustomerGroupRepository>();

            // Ensure AuditTrailService is registered
            services.TryAddScoped<IAuditTrailService, AuditTrailService>();

            // Register Domain Services
            services.AddAutoMapper(typeof(CustomerMappingProfile));

            return services;
        }
    }
}