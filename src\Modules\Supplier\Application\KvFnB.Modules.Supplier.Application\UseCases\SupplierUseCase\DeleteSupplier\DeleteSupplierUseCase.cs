﻿using Dapper;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Constants;
using KvFnB.Core.Contracts;
using KvFnB.Core.Utilities;
using KvFnB.Core.Validation;
using KvFnB.Modules.Supplier.Domain.Enums;
using KvFnB.Modules.Supplier.Domain.Models;
using KvFnB.Modules.Supplier.Domain.Repositories;
using System;

namespace KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.DeleteSupplier
{
    public class DeleteSupplierUseCase : UseCaseBase<DeleteSupplierRequest, DeleteSupplierResponse>
    {
        private readonly IValidator<DeleteSupplierRequest> _validator;
        private readonly IQueryService _queryService;
        private readonly ISupplierRepository _supplierRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger _logger;

        public DeleteSupplierUseCase(
            IValidator<DeleteSupplierRequest> validator,
            IQueryService queryService,
            ISupplierRepository supplierRepository,
            IUnitOfWork unitOfWork,
            ILogger logger)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
            _supplierRepository = supplierRepository ?? throw new ArgumentNullException(nameof(supplierRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public override async Task<Result<DeleteSupplierResponse>> ExecuteAsync(
        DeleteSupplierRequest request,
        CancellationToken cancellationToken = default)
        {
            try
            {
                var validationResult = _validator.Validate(request);
                if (!validationResult.IsValid)
                {
                    return Result<DeleteSupplierResponse>.Failure(validationResult.Errors);
                }

                var sql = @"SELECT TOP 1 * FROM Supplier WITH(NOLOCK) WHERE Id = @id AND (IsDeleted = 0 OR IsDeleted IS NULL)";
                var parameters = new DynamicParameters();
                parameters.Add("id", request.SupplierId);

                var supplier = (await _queryService.QueryPlainTextAsync<Domain.Models.Supplier>(sql, parameters)).FirstOrDefault();

                if (supplier == null)
                {
                    return Result<DeleteSupplierResponse>.Failure("Nhà cung cấp không tồn tại hoặc đã bị xóa khỏi hệ thống");
                }

                supplier.IsDeleted = true;
                supplier.Code = await GetNextUniqueDeleteAsync(supplier.Code, SupplierIdentifierType.Code);
                supplier.Name = string.IsNullOrEmpty(supplier.Name) ? string.Empty : await GetNextUniqueDeleteAsync(supplier.Name, SupplierIdentifierType.Name);
                supplier.Phone = string.IsNullOrEmpty(supplier.Phone) ? string.Empty : await GetNextUniqueDeleteAsync(supplier.Phone, SupplierIdentifierType.Phone);

                await _supplierRepository.UpdateAsync(supplier, cancellationToken);
                await _unitOfWork.CommitAsync(cancellationToken);

                var response = new DeleteSupplierResponse
                {
                    Id = supplier.Id,
                    Name = supplier.Name ?? string.Empty,
                    Phone = supplier.Phone ?? string.Empty,
                    Code = supplier.Code ?? string.Empty,
                    IsDeleted = supplier.IsDeleted ?? true
                };

                return Result<DeleteSupplierResponse>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.Error(string.Format("Error occurred while deleting supplier with ID {0}", request.SupplierId),ex);
                return Result<DeleteSupplierResponse>.Failure(ErrorMessages.InternalServerError);
            }
        }

        private async Task<string> GetNextUniqueDeleteAsync(string key, SupplierIdentifierType type)
        {
            var tempKey = key;

            if (tempKey.Length > 245 && type != SupplierIdentifierType.Code)
            {
                tempKey = tempKey.Substring(0, 245);
            }

            var tempKeyDel = tempKey + Domain.Models.Supplier.CodeDelSuffix;

            var sql = type switch
            {
                SupplierIdentifierType.Code => "SELECT Code FROM Supplier WHERE Code LIKE @value",
                SupplierIdentifierType.Phone => "SELECT Phone FROM Supplier WHERE Phone LIKE @value",
                SupplierIdentifierType.Name => "SELECT Name FROM Supplier WHERE Name LIKE @value",
                _ => throw new ArgumentOutOfRangeException(nameof(type), type, "Invalid 'type' parameter for supplier query.")
            };

            var value = $"%{tempKeyDel}%";
            var parameters = new DynamicParameters();
            parameters.Add("value", value);

            var results = await _queryService.QueryPlainTextAsync<string>(sql, parameters);

            var lsDeleted = results.ToList();

            if (lsDeleted.Count == 0)
            {
                return $"{tempKey}{Domain.Models.Supplier.CodeDelSuffix}}}";
            }

            var lastKeyDeleted = 0;

            lastKeyDeleted = lsDeleted.Select(c =>
            {
                var field = c;
                var index = field.LastIndexOf(Domain.Models.Supplier.CodeDelSuffix, StringComparison.Ordinal);
                var suffix = index >= 0
                    ? field.Substring(index + Domain.Models.Supplier.CodeDelSuffix.Length).TrimEnd('}')
                    : "0";
                return ConvertHelper.ToInt(suffix);
            }).OrderByDescending(o => o).FirstOrDefault();

            var suffix = lastKeyDeleted < 0 ? Domain.Models.Supplier.CodeDelSuffix : $"{Domain.Models.Supplier.CodeDelSuffix}{lastKeyDeleted + 1}";
            return $"{tempKey}{suffix}}}";
        }
    }
}
