using System.Reflection;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Domain;
using KvFnB.Shared.Persistence.ShardingDb.EntityTypeConfigurations;
using KvFnB.Shared.Persistence.ShardingDb.Extensions;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore;
using KvFnB.Core.Authentication;
using Microsoft.EntityFrameworkCore.Storage;

namespace KvFnB.Shared.Persistence.ShardingDb
{
    
    /*
        * ShardingDbContext is a DbContext that is used for sharding database.
        * It is responsible for applying query filters for tenant and soft delete.
        * It also applies audit information for entities that implement IAuditableEntity.
        * NOTICE: Only use primary constructor for migration and testing.
    */
    public class ShardingDbContext : DbContext, IUnitOfWork
    {
        private readonly ITenantProvider _tenantProvider;
        private readonly IAuthUser _authUser;
        private IDbContextTransaction? _currentTransaction;
        public ShardingDbContext(ITenantProvider tenantProvider, IAuthUser authUser, DbContextOptions<ShardingDbContext> options)
            : base(options)
        {
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _authUser = authUser ?? throw new ArgumentNullException(nameof(authUser));
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            var configNamespace = typeof(CategoryEntityTypeConfiguration).Namespace;
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly(),
                type => type.Namespace == configNamespace && type.GetInterfaces().Any(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IEntityTypeConfiguration<>)));
            modelBuilder.HasQueryFilter<ITenantEntity>(e => e.TenantId == _tenantProvider!.GetTenantId());
            modelBuilder.ApplyHasTrigger();
        }
    
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                optionsBuilder.UseSqlServer(options => 
                {
                    options.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(5),
                        errorNumbersToAdd: [4060, 40197, 40501, 40613, 49918, 49919, 49920]
                    );
                    options.CommandTimeout(30);
                    options.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
                });
                optionsBuilder.EnableSensitiveDataLogging();
                optionsBuilder.EnableDetailedErrors();
            }
        }

        public async Task CommitAsync(CancellationToken cancellationToken = default)
        {
            await SaveChangesAsync(cancellationToken);
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            
            ChangeTracker.ApplyAuditInformation(_tenantProvider!, _authUser!);
            return base.SaveChangesAsync(cancellationToken);
        }
        
        public bool HasActiveTransaction => _currentTransaction != null;

        public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
        {
            if (_currentTransaction != null) return;

            _currentTransaction = await Database.BeginTransactionAsync(cancellationToken);
        }

        public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
        {
            if (_currentTransaction == null)
            {
                throw new InvalidOperationException("No active transaction to commit.");
            }

            try
            {
                await SaveChangesAsync(cancellationToken);
                await _currentTransaction!.CommitAsync(cancellationToken);
            }
            finally
            {
                if (_currentTransaction != null)
                {
                    await _currentTransaction.DisposeAsync();
                    _currentTransaction = null;
                }
            }
        }

        public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
        {
            if (_currentTransaction == null)
            {
                throw new InvalidOperationException("No active transaction to rollback.");
            }

            try
            {
                await _currentTransaction.RollbackAsync(cancellationToken);
            }
            finally
            {
                if (_currentTransaction != null)
                {
                    await _currentTransaction.DisposeAsync();
                    _currentTransaction = null;
                }
            }
        }
    }
}