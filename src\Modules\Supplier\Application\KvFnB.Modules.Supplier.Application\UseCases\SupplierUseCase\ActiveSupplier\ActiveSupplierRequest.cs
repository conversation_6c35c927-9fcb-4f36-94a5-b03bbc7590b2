﻿using System.ComponentModel;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Supplier.Application.UseCases.SupplierUseCase.ActiveSupplier
{
    /// <summary>
    /// Represents the request model for activating a supplier
    /// </summary>
    public record ActiveSupplierRequest
    {
        /// <summary>
        /// The ID of the supplier to activate
        /// </summary>
        [JsonPropertyName("supplier_id"), Description("The ID of the supplier to activate.")]
        public int SupplierId { get; init; }
    }
}
