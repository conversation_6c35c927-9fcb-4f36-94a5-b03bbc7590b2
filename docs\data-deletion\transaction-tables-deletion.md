# Technical Specification: Transaction Tables Deletion Process

## 1. Overview

This document details the technical implementation for deleting data from the main transaction tables listed in section 5.1 of the Data Deletion Technical Specification:
- Invoice
- Payment
- PurchaseOrder
- PurchaseReturn
- Return
- Transfer
- CashFlow
- Manufacturing
- DamageItem
- Order
- OrderSummaryInfo
- StockTake
- InventoryTracking
- BalanceTracking
- BalanceAdjustment
- CostAdjustment

## 1.1 Stored Procedures Summary

This document defines the following stored procedures:
1. Invoice
2. PurchaseOrder
3. PurchaseReturn
4. Return
5. Transfer
6. Payment
7. CashFlow
8. Manufacturing
9. DamageItem
10. Order
10. OrderSummaryInfo
11. StockTake
12. InventoryTracking
13. BalanceTracking
14. BalanceAdjustment
15. CostAdjustment
16. PartnerOrder
17. PointTracking
18. PurcharsePayment

### Main Deletion Procedures
1. `pr_DataManagement_DeleteInvoiceData` - Deletes records from Invoice table
2. `pr_DataManagement_DeletePurchaseOrderData` - Deletes records from PurchaseOrder table
3. `pr_DataManagement_DeletePurchaseReturnData` - Deletes records from PurchaseReturn table
4. `pr_DataManagement_DeleteReturnData` - Deletes records from Return table
5. `pr_DataManagement_DeleteTransferData` - Deletes records from Transfer table
6. `pr_DataManagement_DeletePaymentData` - Deletes records from Payment table
7. `pr_DataManagement_DeleteCashFlowData` - Deletes records from CashFlow table
8. `pr_DataManagement_DeleteManufacturingData` - Deletes records from Manufacturing table
9. `pr_DataManagement_DeleteDamageItemData` - Deletes records from DamageItem table
10. `pr_DataManagement_DeleteOrderData` - Deletes records from Order, OrderSummaryInfo table
11. `pr_DataManagement_DeleteStockTakeData` - Deletes records from StockTake table
12. `pr_DataManagement_DeleteInventoryTrackingData` - Deletes records from InventoryTracking table
13. `pr_DataManagement_DeleteBalanceTrackingData` - Deletes records from BalanceTracking table
14. `pr_DataManagement_DeleteBalanceAdjustmentData` - Deletes records from BalanceAdjustment table
15. `pr_DataManagement_DeleteCostAdjustmentData` - Deletes records from CostAdjustment table
16. `pr_DataManagement_DeletePartnerOrderData` - Deletes records from PartnerOrder table
17. `pr_DataManagement_DeletePointTrackingData` - Deletes records from PointTracking table
17. `pr_DataManagement_DeletePointTrackingData` - Deletes records from PointTracking table
18. `pr_DataManagement_DeletePurchasePaymentData` - Deletes records from PurchasePayment table

### Balance and Adjustment Procedures
17. `pr_DataManagement_GeneratePostDeleteAdjustmentForCustomer` - Generates post-deletion adjustments for customer balances
18. `pr_DataManagement_GeneratePostDeleteAdjustmentForSupplier` - Generates post-deletion adjustments for supplier balances
19. `pr_DataManagement_GeneratePostDeleteAdjustmentForProduct` - Generates post-deletion adjustments for product inventory
20. `pr_DataManagement_GeneratePostDeleteAdjustmentForPartnerDelivery` - Generates post-deletion adjustments for partnerDelivery balances

### Restoration Process
21. `sp_RestoreRetailerTransactionData` - Restores previously deleted transaction data

## 2. Implementation Strategy

### 2.1. Deletion Sequence

Transaction tables must be processed in a specific order to maintain referential integrity:

1. Master records (Invoice, PurchaseOrder, etc.)
2. Related transaction records (Payment, CashFlow)
3. Balance and tracking records

The recommended order of processing:

```
1. Invoice
2. PurchaseOrder
3. PurchaseReturn
4. Return
5. Transfer
6. Payment
7. CashFlow
8. Manufacturing
9. DamageItem
10. Order
10. OrderSummaryInfo
11. StockTake
12. InventoryTracking
13. BalanceTracking
14. BalanceAdjustment
15. CostAdjustment
```

## 3. Table-Specific Implementation Details
pr_DataManagement_DeleteInvoiceData
### 3.1. Invoice Procedure

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeleteInvoiceData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT,
    @IgnoreEinvoice BIT = 0
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeleteInvoice'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            IF @IgnoreEinvoice = 1
            BEGIN
                -- When IgnoreEinvoice=1, we still delete invoices, but skip those with e-invoices
                UPDATE TOP (@BatchSize) i WITH (ROWLOCK)
                SET RetailerId = @NewRetailerId, ModifiedDate = GETDATE()
                FROM Invoice i
                WHERE i.RetailerId = @RetailerId
                AND (@FromDate IS NULL OR i.PurchaseDate >= @FromDate)
                AND (@ToDate IS NULL OR i.PurchaseDate <= @ToDate)
                AND i.BranchId = @BranchId
                AND NOT EXISTS (
                    SELECT 1 
                    FROM EInvoice e 
                    WHERE e.InvoiceId = i.Id AND e.RetailerId = i.RetailerId
                );
            END
            ELSE
            BEGIN
                -- When IgnoreEinvoice=0, update all invoices regardless of e-invoice association
                UPDATE TOP (@BatchSize) i WITH (ROWLOCK)
                SET RetailerId = @NewRetailerId, ModifiedDate = GETDATE(), BookingTitle = Code, Code = NEWID()
                FROM Invoice i
                WHERE i.RetailerId = @RetailerId
                AND (@FromDate IS NULL OR i.PurchaseDate >= @FromDate)
                AND (@ToDate IS NULL OR i.PurchaseDate <= @ToDate)
                AND i.BranchId = @BranchId;
                
            END
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END

        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            IF @IgnoreEinvoice = 0
            BEGIN
                -- Also update any related EInvoice records directly based on the same date criteria
                UPDATE TOP (@BatchSize) e WITH (ROWLOCK)
                SET RetailerId = @NewRetailerId, ModifedDate = GETDATE()
                FROM EInvoice e
                WHERE e.RetailerId = @RetailerId
                AND (@FromDate IS NULL OR e.CreatedDate >= @FromDate)
                AND (@ToDate IS NULL OR e.CreatedDate <= @ToDate)
                AND e.BranchId = @BranchId;
            END
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        -- Silently continue execution without any logging or error notifications
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeleteInvoice'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.2. PurchaseOrder Procedure

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeletePurchaseOrderData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeletePurchaseOrder'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) PurchaseOrder WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId, ModifiedDate = GETDATE(), Description = Code, Code = NEWID()
            WHERE RetailerId = @RetailerId
            AND (@FromDate IS NULL OR PurchaseDate >= @FromDate)
            AND (@ToDate IS NULL OR PurchaseDate <= @ToDate)
            AND BranchId = @BranchId;
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeletePurchaseOrder'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.3. PurchaseReturn Procedure

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeletePurchaseReturnData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeletePurchaseReturn'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) PurchaseReturn WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId, ModifiedDate = GETDATE()
            WHERE RetailerId = @RetailerId
            AND (@FromDate IS NULL OR ReturnDate >= @FromDate)
            AND (@ToDate IS NULL OR ReturnDate <= @ToDate)
            AND BranchId = @BranchId;
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeletePurchaseReturn'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.4. Return Procedure

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeleteReturnData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeleteReturn'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) [Return] WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId, ModifiedDate = GETDATE()
            WHERE RetailerId = @RetailerId
            AND (@FromDate IS NULL OR ReturnDate >= @FromDate)
            AND (@ToDate IS NULL OR ReturnDate <= @ToDate)
            AND BranchId = @BranchId;
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeleteReturn'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.5. Transfer and TransferDetail

Special handling for branch-to-branch inventory transfers:

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeleteTransferData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeleteTransfer'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) Transfer WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId, ModifiedDate = GETDATE(), Description = Code, Code = NEWID()
            WHERE RetailerId = @RetailerId
            AND (@FromDate IS NULL OR DispatchedDate >= @FromDate)
            AND (@ToDate IS NULL OR DispatchedDate <= @ToDate)
            AND (FromBranchId = @BranchId OR ToBranchId = @BranchId);
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeleteTransfer'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.6. Payment

``` sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeletePaymentData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeletePayment'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) Payment WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId, ModifiedDate = GETDATE()
            WHERE RetailerId = @RetailerId
            AND (@FromDate IS NULL OR TransDate >= @FromDate)
            AND (@ToDate IS NULL OR TransDate <= @ToDate)
            AND BranchId = @BranchId;
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeletePayment'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.7. CashFlow

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeleteCashFlowData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
      
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeleteCashFlow'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) CashFlow WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId, ModifiedDate = GETDATE()
            WHERE RetailerId = @RetailerId
            AND (@FromDate IS NULL OR PaymentDate >= @FromDate)
            AND (@ToDate IS NULL OR PaymentDate <= @ToDate)
            AND BranchId = @BranchId;
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeleteCashFlow'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.8. Manufacturing

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeleteManufacturingData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeleteManufacturing'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) Manufacturing WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId, ModifiedDate = GETDATE()
            WHERE RetailerId = @RetailerId
            AND (@FromDate IS NULL OR ManufacturingDate >= @FromDate)
            AND (@ToDate IS NULL OR ManufacturingDate <= @ToDate)
            AND BranchId = @BranchId;
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeleteManufacturing'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.9. DamageItem Procedure

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeleteDamageItemData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeleteDamageItem'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) DamageItem WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId, ModifiedDate = GETDATE()
            WHERE RetailerId = @RetailerId
            AND (@FromDate IS NULL OR TransDate >= @FromDate)
            AND (@ToDate IS NULL OR TransDate <= @ToDate)
            AND BranchId = @BranchId;
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeleteDamageItem'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.10. Order Procedure

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeleteOrderData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeleteOrder'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) [Order] WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId, ModifiedDate = GETDATE(), BookingTitle = Code, Code = NEWID()
            WHERE RetailerId = @RetailerId
            AND (@FromDate IS NULL OR PurchaseDate >= @FromDate)
            AND (@ToDate IS NULL OR PurchaseDate <= @ToDate)
            AND BranchId = @BranchId
            AND [Status] IN (3,4,5);
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END

        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) [OrderSummaryInfo] WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId
            WHERE RetailerId = @RetailerId
            AND (@FromDate IS NULL OR PurchaseDate >= @FromDate)
            AND (@ToDate IS NULL OR PurchaseDate <= @ToDate)
            AND BranchId = @BranchId
            AND [Status] != 1;

            SET @RowsUpdated = @@ROWCOUNT;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeleteOrder'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.11. StockTake Procedure

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeleteStockTakeData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeleteStockTake'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) StockTake WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId, ModifiedDate = GETDATE()
            WHERE RetailerId = @RetailerId
            AND (@FromDate IS NULL OR AdjustmentDate >= @FromDate)
            AND (@ToDate IS NULL OR AdjustmentDate <= @ToDate)
            AND BranchId = @BranchId;
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeleteStockTake'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.12. InventoryTracking Procedure

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeleteInventoryTrackingData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeleteInventoryTracking'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            -- Only update records that are not auto-generated by system adjustments
            UPDATE TOP (@BatchSize) it WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId
            FROM InventoryTracking it
            WHERE it.RetailerId = @RetailerId
            AND (@FromDate IS NULL OR it.TransDate >= @FromDate)
            AND (@ToDate IS NULL OR it.TransDate <= @ToDate)
            AND (@BranchId IS NULL OR it.BranchId = @BranchId)
            AND NOT EXISTS (
                SELECT 1 
                FROM BalanceAdjustmentAutoGenerated baa 
                WHERE baa.BalanceAdjustmentId = it.DocumentId 
                AND baa.Type = it.DocumentType
                AND baa.RetailerId = it.RetailerId
                AND baa.BranchId = it.BranchId
                AND baa.RequestId = @RequestId
            );
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeleteInventoryTracking'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.13. BalanceTracking Procedure

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeleteBalanceTrackingData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeleteBalanceTracking'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            -- Only update records that are not auto-generated by system adjustments
            UPDATE TOP (@BatchSize) bt WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId
            FROM BalanceTracking bt
            WHERE bt.RetailerId = @RetailerId
            AND (@FromDate IS NULL OR bt.TransDate >= @FromDate)
            AND (@ToDate IS NULL OR bt.TransDate <= @ToDate)
            AND NOT EXISTS (
                SELECT 1 
                FROM BalanceAdjustmentAutoGenerated baa 
                WHERE baa.BalanceAdjustmentId = bt.DocumentId 
                AND baa.Type = bt.DocumentType
                AND baa.RetailerId = bt.RetailerId
                
                AND baa.RequestId = @RequestId
            );
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeleteBalanceTracking'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.14. BalanceAdjustment Procedure

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeleteBalanceAdjustmentData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeleteBalanceAdjustment'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) ba WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId
            FROM BalanceAdjustment ba
            WHERE ba.RetailerId = @RetailerId
            AND (@FromDate IS NULL OR ba.AdjustmentDate >= @FromDate)
            AND (@ToDate IS NULL OR ba.AdjustmentDate <= @ToDate)
            AND NOT EXISTS (
                SELECT 1 
                FROM BalanceAdjustmentAutoGenerated baa 
                WHERE baa.BalanceAdjustmentId = ba.Id
                AND baa.Type = 1 -- Type = 1 for balance adjustment
                AND baa.RetailerId = ba.RetailerId
                AND baa.RequestId = @RequestId
            );

            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeleteBalanceAdjustment'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.15. CostAdjustment Procedure

```sql
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER   PROCEDURE [dbo].[pr_DataManagement_DeleteCostAdjustmentData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeleteCostAdjustment'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
       
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) ca WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId
            FROM CostAdjustment ca
            WHERE ca.RetailerId = @RetailerId
            AND (@FromDate IS NULL OR ca.AdjustmentDate >= @FromDate)
            AND (@ToDate IS NULL OR ca.AdjustmentDate <= @ToDate)
            AND ca.BranchId = @BranchId
            AND NOT EXISTS (
                SELECT 1 
                FROM BalanceAdjustmentAutoGenerated baa 
                WHERE baa.BalanceAdjustmentId = ca.Id
                AND baa.Type = 13 -- Type = 13 for cost adjustment
                AND baa.RetailerId = ca.RetailerId
                AND baa.RequestId = @RequestId
            );
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeleteCostAdjustment'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
GO

```

### 3.16. PartnerOrder

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeletePartnerOrderData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeletePartnerOrder'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) PartnerOrder WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId
            WHERE RetailerId = @RetailerId
            AND (@FromDate IS NULL OR OrderTime >= @FromDate)
            AND (@ToDate IS NULL OR OrderTime <= @ToDate)
            AND BranchId = @BranchId;
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeletePartnerOrder'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.17. PointTracking Procedure

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeletePointTrackingData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeletePointTracking'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already processed (status = 2), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 2
        BEGIN
            -- Request either does not exist or has already been processed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) pt WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId
            FROM PointTracking pt
            WHERE pt.RetailerId = @RetailerId
            AND (@FromDate IS NULL OR pt.TransDate >= @FromDate)
            AND (@ToDate IS NULL OR pt.TransDate <= @ToDate)
            AND NOT EXISTS (
                SELECT 1 
                FROM BalanceAdjustmentAutoGenerated baa 
                WHERE baa.BalanceAdjustmentId = pt.DocumentId
                AND baa.Type = 2 -- Type = 2 for point adjustment
                AND baa.RetailerId = pt.RetailerId
                
                AND baa.RequestId = @RequestId
            );
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeletePointTracking'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 3.18. PurchasePayment Procedure

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_DeletePurchasePaymentData]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @NewRetailerId INT;
    DECLARE @RowsUpdated INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'DeletePurchasePayment'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Calculate new RetailerId based on the formula
        SET @NewRetailerId = -@RequestId 
        
        -- Check if new RetailerId exists in Retailer table
        -- If not, create it to prevent foreign key constraint violation
        IF NOT EXISTS(SELECT 1 FROM Retailer WHERE Id = @NewRetailerId)
        BEGIN
            -- Begin a transaction for creating the new Retailer record
            BEGIN TRY
                BEGIN TRANSACTION;
                
                -- Get original retailer data for reference
                DECLARE @OriginalCompanyName NVARCHAR(125)
                DECLARE @OriginalCode NVARCHAR(100)
                DECLARE @OriginalGroupId INT
                
                SELECT 
                    @OriginalCompanyName = CompanyName,
                    @OriginalCode = Code,
                    @OriginalGroupId = GroupId
                FROM Retailer 
                WHERE Id = @RetailerId;
                
                -- Insert a new Retailer record with the negative RetailerId
                INSERT INTO Retailer (
                    Id,
                    CompanyName,
                    CompanyAddress,
                    Code,
                    IsActive,
                    IsAdminActive,
                    GroupId,
                    CreatedDate,
                    UseCustomLogo,
                    CreatedBy
                )
                VALUES (
                    @NewRetailerId,
                    ISNULL(@OriginalCompanyName, 'Deleted Retailer - ' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    'Deleted Address',
                    ISNULL(@OriginalCode + '_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50)), 'DELETED_' + CAST(ABS(@NewRetailerId) AS NVARCHAR(50))),
                    0, -- IsActive = false
                    0, -- IsAdminActive = false
                    ISNULL(@OriginalGroupId, 1), -- Default GroupId or use original
                    GETDATE(),
                    0, -- UseCustomLogo = false
                    1  -- CreatedBy = System
                );
                
                -- Commit the retailer creation transaction
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                -- If there is an active transaction, roll it back
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                -- Silently continue execution without any logging or error notifications
            END CATCH
        END
        
        -- Loop until no more rows are updated or time limit is reached
        WHILE GETDATE() < @EndTime
        BEGIN
            -- Begin a transaction for this batch
            BEGIN TRANSACTION;
            
            -- Update a batch of rows
            UPDATE TOP (@BatchSize) PurchasePayment WITH (ROWLOCK)
            SET RetailerId = @NewRetailerId, ModifiedDate = GETDATE()
            WHERE RetailerId = @RetailerId
            AND (@FromDate IS NULL OR TransDate >= @FromDate)
            AND (@ToDate IS NULL OR TransDate <= @ToDate)
            AND BranchId = @BranchId;
            
            SET @RowsUpdated = @@ROWCOUNT;
            SET @TotalRowsUpdated = @TotalRowsUpdated + @RowsUpdated;
            
            -- Commit the transaction for this batch
            COMMIT TRANSACTION;
            
            -- Exit the loop if no more rows were updated
            IF @RowsUpdated = 0
                BREAK;
            
            -- Add a small delay to reduce resource contention
            WAITFOR DELAY '00:00:00.100';
        END
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'DeletePurchasePayment'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

## 4. Transaction Balance Integrity

### 4.1. Inventory Balance Adjustments

This section has been removed.

### 4.2. Debt Balance Adjustments

Similarly, for debt-affecting transactions (Invoice, Payment, PurchaseOrder, etc.), debt balance adjustments are created:

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_GeneratePostDeleteAdjustmentForCustomer]
    @RetailerId INT,
    @RequestId BIGINT,
    @BatchSize INT = 5000,
    @MaxExecutionSeconds INT = 600,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @BranchId INT,
    @UserId INT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, @MaxExecutionSeconds, @StartTime);
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Now DATETIME = GETDATE();
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    
    -- Customer variables for cursor
    DECLARE @CustomerId BIGINT;
    DECLARE @CustomerDebt DECIMAL(18, 4);
    DECLARE @CustomerRewardPoint DECIMAL(18, 4);
    DECLARE @BalanceAdjustmentId BIGINT;
    DECLARE @PointAdjustmentId BIGINT;
    DECLARE @BalanceAdjustmentCode NVARCHAR(50);
    DECLARE @PointAdjustmentCode NVARCHAR(50);
    DECLARE @BalanceAdjustmentDescription NVARCHAR(255);
    DECLARE @PointAdjustmentDescription NVARCHAR(255);
    DECLARE @BalanceSequentialNumber INT;
    DECLARE @PointSequentialNumber INT;

    BEGIN TRY
        -- Get an admin user
        SELECT TOP 1 @UserId = CAST(Id AS INT)
        FROM [User]
        WHERE RetailerId = @RetailerId 
        AND IsAdmin = 1 
        AND IsActive = 1
        AND ISNULL(isDeleted, 0) = 0;
        
        -- If no admin user found, use default system user
        IF @UserId IS NULL
            SET @UserId = 1;

        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Validate BranchId
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'AdjustmentCustomer'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Create description templates
        SET @BalanceAdjustmentDescription = N'Điều chỉnh công nợ khách hàng tự động sau khi xóa giao dịch và thu chi vào ' + FORMAT(GETDATE(), 'dd/MM/yyyy');
        SET @PointAdjustmentDescription = N'Điều chỉnh điểm khách hàng tự động sau khi xóa giao dịch và thu chi vào ' + FORMAT(GETDATE(), 'dd/MM/yyyy');
        
        -- Declare cursor to iterate through all customers
        DECLARE CustomerCursor CURSOR FOR
        SELECT 
            Id,
            Debt,
            RewardPoint
        FROM 
            Customer c
        WHERE 
            c.RetailerId = @RetailerId
            AND (c.RewardPoint > 0 OR c.Debt != 0)
            AND c.IsActive = 1
            AND ISNULL(c.isDeleted, 0) = 0;
        
        OPEN CustomerCursor;
        FETCH NEXT FROM CustomerCursor INTO @CustomerId, @CustomerDebt, @CustomerRewardPoint;
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            -- Begin transaction for this customer
            BEGIN TRANSACTION;
            
            -- 1. BALANCE ADJUSTMENT PROCESSING - Only if CustomerDebt has a value
            IF @CustomerDebt IS NOT NULL AND @CustomerDebt != 0
            BEGIN
                -- Generate sequential number for balance adjustment code
                BEGIN TRANSACTION;
                
                IF EXISTS (SELECT 1 FROM AutoGeneratedCode WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD')
                BEGIN
                    -- Get current value and update it atomically
                    SELECT @BalanceSequentialNumber = CAST(Value AS INT) + 1
                    FROM AutoGeneratedCode WITH (UPDLOCK, ROWLOCK)
                    WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD';
                    
                    -- Update the incremented value
                    UPDATE AutoGeneratedCode
                    SET Value = @BalanceSequentialNumber
                    WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD';
                END
                ELSE
                BEGIN
                    -- Insert new record if it doesn't exist
                    SET @BalanceSequentialNumber = 1;
                    
                    INSERT INTO AutoGeneratedCode (RetailerId, TypeOf, Prefix, Value)
                    VALUES (@RetailerId, 'AutoBalanced', 'CBTD', @BalanceSequentialNumber);
                END
                
                COMMIT TRANSACTION;
                
                -- Format the code with padded zeros
                SET @BalanceAdjustmentCode = 'CBTD' + RIGHT('000000' + CAST(@BalanceSequentialNumber AS NVARCHAR(10)), 6);
                
                -- Insert BalanceAdjustment for this customer
                INSERT INTO BalanceAdjustment (
                    Code,
                    RetailerId,
                    AdjustedBy,
                    AdjustmentDate,
                    CreatedDate,
                    CreatedBy,
                    Description,
                    Balance,
                    PartnerType,
                    PartnerId
                )
                VALUES (
                    @BalanceAdjustmentCode,
                    @RetailerId,
                    @UserId,
                    @Now,
                    @Now,
                    @UserId,
                    @BalanceAdjustmentDescription,
                    @CustomerDebt,
                    0, -- PartnerType = 0 for Customer
                    @CustomerId           
                );
                
                SET @BalanceAdjustmentId = SCOPE_IDENTITY();
                SET @TotalRowsUpdated = @TotalRowsUpdated + 1;
                
                -- Record in auto-generated table for BalanceAdjustment
                INSERT INTO BalanceAdjustmentAutoGenerated (
                    RetailerId,
                    BranchId,
                    BalanceAdjustmentId,
                    RequestId,
                    Type,
                    CreatedDate
                )
                VALUES (
                    @RetailerId,
                    @BranchId,
                    @BalanceAdjustmentId,
                    @RequestId,
                    1, -- Type = 1 for balance adjustment
                    @Now
                );
                
                -- Insert into BalanceTracking for this customer
                INSERT INTO BalanceTracking (
                    PartnerId,
                    DocumentId,
                    DocumentCode,
                    DocumentType,
                    Description,
                    Value,
                    Balance,
                    RetailerId,
                    TransDate,
                    DataZone
                )
                VALUES (
                    @CustomerId,
                    @BalanceAdjustmentId,
                    @BalanceAdjustmentCode,
                    1, -- DocumentType = 1 for balance adjustment
                    @BalanceAdjustmentDescription,
                    @CustomerDebt, -- No value change 
                    @CustomerDebt, -- Current debt balance
                    @RetailerId,
                    @Now,
                    0
                );
                
                SET @TotalRowsUpdated = @TotalRowsUpdated + 1;
            END
            
            -- 2. POINT ADJUSTMENT PROCESSING - Only if CustomerRewardPoint has a value
            IF @CustomerRewardPoint IS NOT NULL  AND @CustomerRewardPoint > 0
            BEGIN
                -- Generate sequential number for point adjustment code
                BEGIN TRANSACTION;
                
                IF EXISTS (SELECT 1 FROM AutoGeneratedCode WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD')
                BEGIN
                    -- Get current value and update it atomically
                    SELECT @PointSequentialNumber = CAST(Value AS INT) + 1
                    FROM AutoGeneratedCode WITH (UPDLOCK, ROWLOCK)
                    WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD';
                    
                    -- Update the incremented value
                    UPDATE AutoGeneratedCode
                    SET Value = @PointSequentialNumber
                    WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD';
                END
                ELSE
                BEGIN
                    -- Insert new record if it doesn't exist
                    SET @PointSequentialNumber = 1;
                    
                    INSERT INTO AutoGeneratedCode (RetailerId, TypeOf, Prefix, Value)
                    VALUES (@RetailerId, 'AutoBalanced', 'CBTD', @PointSequentialNumber);
                END
                
                COMMIT TRANSACTION;
                
                -- Format the code with padded zeros
                SET @PointAdjustmentCode = 'CBTD' + RIGHT('000000' + CAST(@PointSequentialNumber AS NVARCHAR(10)), 6);
                
                -- Insert PointAdjustment for this customer
                INSERT INTO PointAdjustment (
                    Code,
                    RetailerId,
                    AdjustedBy,
                    AdjustmentDate,
                    CreatedDate,
                    CreatedBy,
                    Description,
                    Balance,
                    PartnerType,
                    PartnerId
                )
                VALUES (
                    @PointAdjustmentCode,
                    @RetailerId,
                    @UserId,
                    @Now,
                    @Now,
                    @UserId,
                    @PointAdjustmentDescription,
                    @CustomerRewardPoint, -- No point balance adjustment
                    0,  -- PartnerType = 0 for Customer
                    @CustomerId
                );
                
                SET @PointAdjustmentId = SCOPE_IDENTITY();
                SET @TotalRowsUpdated = @TotalRowsUpdated + 1;
                
                -- Record in auto-generated table for PointAdjustment
                INSERT INTO BalanceAdjustmentAutoGenerated (
                    RetailerId,
                    BranchId,
                    BalanceAdjustmentId,
                    RequestId,
                    Type,
                    CreatedDate
                )
                VALUES (
                    @RetailerId,
                    @BranchId,
                    @PointAdjustmentId,
                    @RequestId,
                    2, -- Type = 2 for point adjustment
                    @Now
                );
                
                -- Insert into PointTracking for this customer
                INSERT INTO PointTracking (
                    PartnerId,
                    DocumentId,
                    DocumentCode,
                    DocumentType,
                    Description,
                    Amount,
                    Value,
                    Balance,
                    RetailerId,
                    TransDate,
                    DataZone
                )
                VALUES (
                    @CustomerId,
                    @PointAdjustmentId,
                    @PointAdjustmentCode,
                    0, -- DocumentType = 0 for point adjustment
                    @PointAdjustmentDescription,
                    0,
                    @CustomerRewardPoint,
                    @CustomerRewardPoint, -- Current reward point balance
                    @RetailerId,
                    @Now, 
                    0
                );
                
                SET @TotalRowsUpdated = @TotalRowsUpdated + 1;
            END
            
            -- Commit the transaction for this customer
            COMMIT TRANSACTION;
            
            -- Get next customer
            FETCH NEXT FROM CustomerCursor INTO @CustomerId, @CustomerDebt, @CustomerRewardPoint;
        END
        
        CLOSE CustomerCursor;
        DEALLOCATE CustomerCursor;
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        -- Close and deallocate cursor if open
        IF CURSOR_STATUS('local', 'CustomerCursor') >= 0
        BEGIN
            CLOSE CustomerCursor;
            DEALLOCATE CustomerCursor;
        END
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'AdjustmentCustomer'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

### 4.3. Inventory Reconciliation and Cost Adjustment

After transaction data deletion, the system generates StockTake and CostAdjustment records for all products to reconcile the inventory values and costs with the ProductBranch table:

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_GeneratePostDeleteAdjustmentForProduct]
    @RetailerId INT,
    @BranchId INT,
    @RequestId BIGINT,
    @UserId INT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, 600, @StartTime);
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @StockTakeId BIGINT;
    DECLARE @CostAdjustmentId BIGINT;
    DECLARE @StockTakeCode NVARCHAR(50);
    DECLARE @CostAdjustmentCode NVARCHAR(50);
    DECLARE @Now DATETIME = GETDATE();
    DECLARE @StockTakeDescription NVARCHAR(255);
    DECLARE @CostAdjustmentDescription NVARCHAR(255);
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    
    -- Product variables for cursor
    DECLARE @ProductId BIGINT;
    DECLARE @ProductOnHand DECIMAL(18, 4);
    DECLARE @ProductCost DECIMAL(18, 4);
    DECLARE @CostSequentialNumber INT;
    
    SELECT TOP 1 @UserId = CAST(Id AS INT)
        FROM [User]
        WHERE RetailerId = @RetailerId 
        AND IsAdmin = 1 
        AND IsActive = 1
        AND ISNULL(isDeleted, 0) = 0;
        
        -- If no admin user found, use default system user
        IF @UserId IS NULL
            SET @UserId = 1;

    BEGIN TRY
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'AdjustmentProduct'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Begin transaction
        BEGIN TRANSACTION;
        
        -- 1. STOCK TAKE PROCESSING
        -- Generate stocktake code
        -- Format: KKTD (Kiểm Kho Tự Động) + sequential number 
        SELECT @StockTakeCode = 'KKTD' + RIGHT('000000' + CAST(
            ISNULL((SELECT MAX(CAST(SUBSTRING(Code, 5, LEN(Code)) AS INT)) + 1 FROM StockTake 
                  WHERE RetailerId = @RetailerId AND Code LIKE 'KKTD%'), 1) AS VARCHAR(6)), 6);
        
        -- Create description for this reconciliation stocktake
        SET @StockTakeDescription = N'Phiếu kiểm kho tự động sau khi xóa giao dịch và thu chi vào ' + FORMAT(GETDATE(), 'dd/MM/yyyy');
        
        -- Insert StockTake header
        INSERT INTO StockTake (
            Code,
            BranchId,
            RetailerId,
            Status,
            AdjustedBy,
            AdjustmentDate,
            CreatedDate,
            CreatedBy,
            Description
        )
        VALUES (
            @StockTakeCode,
            @BranchId,
            @RetailerId,
            1, -- Completed status
            @UserId,
            @Now,
            @Now,
            @UserId,
            @StockTakeDescription
        );
        
        SET @StockTakeId = SCOPE_IDENTITY();
        SET @TotalRowsUpdated = @TotalRowsUpdated + 1;
        
        -- Record in auto-generated table for StockTake
        INSERT INTO BalanceAdjustmentAutoGenerated (
            RetailerId,
            BranchId,
            BalanceAdjustmentId,
            RequestId,
            Type,
            CreatedDate
        )
        VALUES (
            @RetailerId,
            @BranchId,
            @StockTakeId,
            @RequestId,
            3, -- Type = 3 for stocktake
            @Now
        );
        
        -- Insert StockTakeDetail for all products in the branch
        INSERT INTO StockTakeDetail (
            StockTakeId,
            ProductId,
            SystemCount,
            ActualCount,
            Cost,
            CreatedDate
        )
        SELECT 
            @StockTakeId,
            pb.ProductId,
            pb.OnHand, -- Current system quantity
            pb.OnHand, -- Setting actual count same as system (no adjustment) No adjustment value
            ISNULL(pb.Cost, 0),
            @Now
        FROM 
            ProductBranch pb
        JOIN Product p ON pb.ProductId = p.Id 
            AND (p.InventoryTrackingIgnore IS NULL OR p.InventoryTrackingIgnore = 0) 
            AND (p.isDeleted IS NULL OR p.isDeleted = 0) 
        WHERE 
            pb.OnHand != 0
            AND pb.BranchId = @BranchId
            AND pb.RetailerId = @RetailerId
            
        SET @TotalRowsUpdated = @TotalRowsUpdated + @@ROWCOUNT;
        
        -- Insert into InventoryTracking table to record the stocktake
        INSERT INTO InventoryTracking (
            ProductId,
            Quantity,
            EndingStocks,
            DocumentCode,
            DocumentType,
            BranchId,
            RetailerId,
            Price,
            Cost,
            TransDate,
            DocumentId,
            HiddenCost,
            IsSpecial
        )
        SELECT 
            pb.ProductId,
            0, -- No quantity change for reconciliation
            pb.OnHand, -- Current ending stock
            @StockTakeCode, -- Document code is the stocktake code
            3, -- DocumentType = 3 for stocktake
            @BranchId,
            @RetailerId,
            0, -- No price for stocktake
            ISNULL(pb.Cost, 0),
            @Now, -- Transaction date
            @StockTakeId, -- DocumentId is the stocktake ID
            0, -- No hidden cost
            0 -- Not special
        FROM 
            ProductBranch pb
            JOIN Product p ON pb.ProductId = p.Id 
                AND (p.InventoryTrackingIgnore IS NULL OR p.InventoryTrackingIgnore = 0) 
                AND (p.isDeleted IS NULL OR p.isDeleted = 0) 
        WHERE 
            pb.OnHand != 0
            AND pb.BranchId = @BranchId
            AND pb.RetailerId = @RetailerId
            
        SET @TotalRowsUpdated = @TotalRowsUpdated + @@ROWCOUNT;
        
        -- Commit the StockTake transaction
        COMMIT TRANSACTION;
            
        -- Create description for cost adjustment
        SET @CostAdjustmentDescription = N'Điều chỉnh giá vốn tự động sau khi xóa giao dịch và thu chi vào ' + FORMAT(GETDATE(), 'dd/MM/yyyy');
        
        -- 2. COST ADJUSTMENT PROCESSING - One per product
        -- Declare cursor to iterate through all products in the branch
        DECLARE ProductCursor CURSOR FOR
        SELECT 
            pb.ProductId,
            pb.OnHand,
            ISNULL(pb.Cost, 0) AS Cost
        FROM 
            ProductBranch pb
        JOIN Product p ON pb.ProductId = p.Id 
            AND (p.isDeleted IS NULL OR p.isDeleted = 0) 
            AND p.ProductType != 1 -- type 1: chế biến
        WHERE 
            pb.Cost > 0
            AND pb.BranchId = @BranchId
            AND pb.RetailerId = @RetailerId
        
        OPEN ProductCursor;
        FETCH NEXT FROM ProductCursor INTO @ProductId, @ProductOnHand, @ProductCost;
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            -- Begin transaction for this product's cost adjustment
            BEGIN TRANSACTION;
            
            -- Generate code for this cost adjustment
            IF EXISTS (SELECT 1 FROM AutoGeneratedCode WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD')
            BEGIN
                -- Get current value and update it atomically in a separate transaction
                BEGIN TRANSACTION;
                
                SELECT @CostSequentialNumber = CAST(Value AS INT) + 1
                FROM AutoGeneratedCode WITH (UPDLOCK, ROWLOCK)
                WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD';
                
                -- Update the incremented value
                UPDATE AutoGeneratedCode
                SET Value = @CostSequentialNumber
                WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD';
                
                COMMIT TRANSACTION;
            END
            ELSE
            BEGIN
                -- Insert new record if it doesn't exist (in a separate transaction)
                BEGIN TRANSACTION;
                
                SET @CostSequentialNumber = 1;
                
                INSERT INTO AutoGeneratedCode (RetailerId, TypeOf, Prefix, Value)
                VALUES (@RetailerId, 'AutoBalanced', 'CBTD', @CostSequentialNumber);
                
                COMMIT TRANSACTION;
            END
            
            -- Format the code with padded zeros
            SET @CostAdjustmentCode = 'CBTD' + RIGHT('000000' + CAST(@CostSequentialNumber AS NVARCHAR(10)), 6);
            
            -- Insert CostAdjustment header for this specific product
            INSERT INTO CostAdjustment (
                Code,
                BranchId,
                RetailerId,
                ProductId,
                Cost,
                AdjustedBy,
                AdjustmentDate,
                CreatedDate,
                CreatedBy
            )
            VALUES (
                @CostAdjustmentCode,
                @BranchId,
                @RetailerId,
                @ProductId,
                @ProductCost,
                @UserId,
                @Now,
                @Now,
                @UserId
            );
            
            SET @CostAdjustmentId = SCOPE_IDENTITY();
            SET @TotalRowsUpdated = @TotalRowsUpdated + 1;
            
            -- Record in auto-generated table for CostAdjustment
            INSERT INTO BalanceAdjustmentAutoGenerated (
                RetailerId,
                BranchId,
                BalanceAdjustmentId,
                RequestId,
                Type,
                CreatedDate
            )
            VALUES (
                @RetailerId,
                @BranchId,
                @CostAdjustmentId,
                @RequestId,
                13, -- Type = 13 for cost adjustment 
                @Now
            );
            
            -- Insert into InventoryTracking table to record the cost adjustment for this product
            INSERT INTO InventoryTracking (
                ProductId,
                Quantity,
                EndingStocks,
                DocumentCode,
                DocumentType,
                BranchId,
                RetailerId,
                Price,
                Cost,
                TransDate,
                DocumentId,
                HiddenCost,
                IsSpecial
            )
            VALUES (
                @ProductId,
                0, -- No quantity change for cost adjustment
                @ProductOnHand, -- Current ending stock
                @CostAdjustmentCode, -- Document code is the cost adjustment code
                13, -- DocumentType = 13 for cost adjustment
                @BranchId,
                @RetailerId,
                0, -- No price for cost adjustment
                @ProductCost,
                @Now, -- Transaction date
                @CostAdjustmentId, -- DocumentId is the cost adjustment ID
                0, -- No hidden cost
                0 -- Not special
            );
            
            SET @TotalRowsUpdated = @TotalRowsUpdated + 1;
            
            -- Commit the transaction for this product
            COMMIT TRANSACTION;
            
            -- Get next product
            FETCH NEXT FROM ProductCursor INTO @ProductId, @ProductOnHand, @ProductCost;
        END
        
        CLOSE ProductCursor;
        DEALLOCATE ProductCursor;
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
        
    END TRY
    BEGIN CATCH
        -- If an error occurs and a transaction is active, roll it back
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Close and deallocate cursor if open
        IF CURSOR_STATUS('local', 'ProductCursor') >= 0
        BEGIN
            CLOSE ProductCursor;
            DEALLOCATE ProductCursor;
        END
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
        
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'AdjustmentProduct'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END


```

for supplier

```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_GeneratePostDeleteAdjustmentForSupplier]
    @RetailerId INT,
    @BranchId INT,
    @RequestId BIGINT,
    @UserId INT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, 600, @StartTime);
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Now DATETIME = GETDATE();
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    
    -- Supplier variables for cursor
    DECLARE @SupplierId BIGINT;
    DECLARE @SupplierDebt DECIMAL(18, 4);
    DECLARE @BalanceAdjustmentId BIGINT;
    DECLARE @BalanceAdjustmentCode NVARCHAR(50);
    DECLARE @BalanceAdjustmentDescription NVARCHAR(255);
    DECLARE @BalanceSequentialNumber INT;
    
    BEGIN TRY
        -- Get an admin user
        SELECT TOP 1 @UserId = CAST(Id AS INT)
        FROM [User]
        WHERE RetailerId = @RetailerId 
        AND IsAdmin = 1 
        AND IsActive = 1
        AND ISNULL(isDeleted, 0) = 0;
        
        -- If no admin user found, use default system user
        IF @UserId IS NULL
            SET @UserId = 1;
            
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'AdjustmentSupplier'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Create description template for balance adjustment
        SET @BalanceAdjustmentDescription = N'Điều chỉnh công nợ nhà cung cấp tự động sau khi xóa giao dịch và thu chi vào ' + FORMAT(GETDATE(), 'dd/MM/yyyy');
        
        -- Declare cursor to iterate through all suppliers
        DECLARE SupplierCursor CURSOR FOR
        SELECT 
            Id,
            Debt
        FROM 
            Supplier s
        WHERE 
            s.RetailerId = @RetailerId
            AND s.Debt != 0
            AND s.IsActive = 1
            AND ISNULL(s.isDeleted, 0) = 0;
        
        OPEN SupplierCursor;
        FETCH NEXT FROM SupplierCursor INTO @SupplierId, @SupplierDebt;
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            -- Begin transaction for this supplier
            BEGIN TRANSACTION;
            
            -- Only proceed if SupplierDebt has a value
            IF @SupplierDebt IS NOT NULL
            BEGIN
                -- Generate sequential number for code
                BEGIN TRANSACTION;
                
                IF EXISTS (SELECT 1 FROM AutoGeneratedCode WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD')
                BEGIN
                    -- Get current value and update it atomically
                    SELECT @BalanceSequentialNumber = CAST(Value AS INT) + 1
                    FROM AutoGeneratedCode WITH (UPDLOCK, ROWLOCK)
                    WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD';
                    
                    -- Update the incremented value
                    UPDATE AutoGeneratedCode
                    SET Value = @BalanceSequentialNumber
                    WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD';
                END
                ELSE
                BEGIN
                    -- Insert new record if it doesn't exist
                    SET @BalanceSequentialNumber = 1;
                    
                    INSERT INTO AutoGeneratedCode (RetailerId, TypeOf, Prefix, Value)
                    VALUES (@RetailerId, 'AutoBalanced', 'CBTD', @BalanceSequentialNumber);
                END
                
                COMMIT TRANSACTION;
                
                -- Format the code with padded zeros
                SET @BalanceAdjustmentCode = 'CBTD' + RIGHT('000000' + CAST(@BalanceSequentialNumber AS NVARCHAR(10)), 6);
                
                -- Insert BalanceAdjustment for this supplier
                INSERT INTO BalanceAdjustment (
                    Code,
                    RetailerId,
                    AdjustedBy,
                    AdjustmentDate,
                    CreatedDate,
                    CreatedBy,
                    Description,
                    Balance,
                    PartnerType,
                    PartnerId
                )
                VALUES (
                    @BalanceAdjustmentCode,
                    @RetailerId,
                    @UserId,
                    @Now,
                    @Now,
                    @UserId,
                    @BalanceAdjustmentDescription,
                    -@SupplierDebt,
                    1,  -- PartnerType = 1 for Supplier
                    @SupplierId
                );
                
                SET @BalanceAdjustmentId = SCOPE_IDENTITY();
                SET @TotalRowsUpdated = @TotalRowsUpdated + 1;
                
                -- Record in auto-generated table for BalanceAdjustment
                INSERT INTO BalanceAdjustmentAutoGenerated (
                    RetailerId,
                    BranchId,
                    BalanceAdjustmentId,
                    RequestId,
                    Type,
                    CreatedDate
                )
                VALUES (
                    @RetailerId,
                    @BranchId,
                    @BalanceAdjustmentId,
                    @RequestId,
                    1, -- Type = 1 for balance adjustment
                    @Now
                );
                
                -- Insert into BalanceTracking for this supplier
                INSERT INTO BalanceTracking (
                    PartnerId,
                    DocumentId,
                    DocumentCode,
                    DocumentType,
                    Description,
                    Value,
                    Balance,
                    RetailerId,
                    TransDate,
                    DataZone
                )
                VALUES (
                    @SupplierId,
                    @BalanceAdjustmentId,
                    @BalanceAdjustmentCode,
                    1, -- DocumentType = 1 for balance adjustment
                    @BalanceAdjustmentDescription,
                    -@SupplierDebt, -- No value change 
                    -@SupplierDebt, -- Current debt balance
                    @RetailerId,
                    @Now,
                    1  -- DataZone = 1 for supplier
                );
                
                SET @TotalRowsUpdated = @TotalRowsUpdated + 1;
            END
            
            -- Commit the transaction for this supplier
            COMMIT TRANSACTION;
            
            -- Get next supplier
            FETCH NEXT FROM SupplierCursor INTO @SupplierId, @SupplierDebt;
        END
        
        CLOSE SupplierCursor;
        DEALLOCATE SupplierCursor;
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        -- Close and deallocate cursor if open
        IF CURSOR_STATUS('local', 'SupplierCursor') >= 0
        BEGIN
            CLOSE SupplierCursor;
            DEALLOCATE SupplierCursor;
        END
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
            
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'AdjustmentSupplier'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

for partner delivery 
```sql
CREATE OR ALTER PROCEDURE [dbo].[pr_DataManagement_GeneratePostDeleteAdjustmentForPartnerDelivery]
    @RetailerId INT,
    @BranchId INT,
    @RequestId BIGINT,
    @UserId INT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    DECLARE @EndTime DATETIME = DATEADD(SECOND, 600, @StartTime);
    DECLARE @ErrorMsg NVARCHAR(1000) = NULL;
    DECLARE @Now DATETIME = GETDATE();
    DECLARE @Status TINYINT = 1; -- Default to Processed (success)
    DECLARE @ProcessedTime INT = 0;
    DECLARE @ExistingStatus INT = 0;
    DECLARE @TotalRowsUpdated INT = 0;
    
    -- PartnerDelivery variables for cursor
    DECLARE @PartnerId BIGINT;
    DECLARE @PartnerDebt DECIMAL(18, 4);
    DECLARE @BalanceAdjustmentId BIGINT;
    DECLARE @BalanceAdjustmentCode NVARCHAR(50);
    DECLARE @BalanceAdjustmentDescription NVARCHAR(255);
    DECLARE @BalanceSequentialNumber INT;
    
    BEGIN TRY
        -- Get an admin user
        SELECT TOP 1 @UserId = CAST(Id AS INT)
        FROM [User]
        WHERE RetailerId = @RetailerId 
        AND IsAdmin = 1 
        AND IsActive = 1
        AND ISNULL(isDeleted, 0) = 0;
        
        -- If no admin user found, use default system user
        IF @UserId IS NULL
            SET @UserId = 1;
            
        -- Validate parameters
        IF @RetailerId <= 0
        BEGIN
            SET @ErrorMsg = 'RetailerId must be a positive integer.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        IF @BranchId IS NULL
        BEGIN
            SET @ErrorMsg = 'BranchId cannot be null.';
            RAISERROR(@ErrorMsg, 16, 1);
        END
        
        -- Check if request already exists and its status
        SELECT @ExistingStatus = [Status]
        FROM DeleteDataDetail
        WHERE RequestId = @RequestId
        AND Type = 'AdjustmentPartnerDelivery'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;

        -- If the request doesn't exist or is already completed (status = 1), return success
        IF @ExistingStatus IS NULL OR @ExistingStatus = 1
        BEGIN
            -- Request either does not exist or has already been completed. No action taken
            RETURN 0;
        END
        
        -- Create description template for balance adjustment
        SET @BalanceAdjustmentDescription = N'Điều chỉnh công nợ đối tác giao hàng tự động sau khi xóa giao dịch và thu chi vào ' + FORMAT(GETDATE(), 'dd/MM/yyyy');
        
        -- Declare cursor to iterate through all partner deliveries
        DECLARE PartnerDeliveryCursor CURSOR FOR
        SELECT 
            Id,
            Debt
        FROM 
            PartnerDelivery pd
        WHERE 
            pd.RetailerId = @RetailerId
            AND pd.Debt != 0
            AND pd.isActive = 1
            AND ISNULL(pd.isDeleted, 0) = 0;
        
        OPEN PartnerDeliveryCursor;
        FETCH NEXT FROM PartnerDeliveryCursor INTO @PartnerId, @PartnerDebt;
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            -- Begin transaction for this partner delivery
            BEGIN TRANSACTION;
            
            -- Only proceed if PartnerDebt has a value
            IF @PartnerDebt IS NOT NULL
            BEGIN
                -- Generate sequential number for code
                BEGIN TRANSACTION;
                
                IF EXISTS (SELECT 1 FROM AutoGeneratedCode WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD')
                BEGIN
                    -- Get current value and update it atomically
                    SELECT @BalanceSequentialNumber = CAST(Value AS INT) + 1
                    FROM AutoGeneratedCode WITH (UPDLOCK, ROWLOCK)
                    WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD';
                    
                    -- Update the incremented value
                    UPDATE AutoGeneratedCode
                    SET Value = @BalanceSequentialNumber
                    WHERE RetailerId = @RetailerId AND TypeOf = 'AutoBalanced' AND Prefix = 'CBTD';
                END
                ELSE
                BEGIN
                    -- Insert new record if it doesn't exist
                    SET @BalanceSequentialNumber = 1;
                    
                    INSERT INTO AutoGeneratedCode (RetailerId, TypeOf, Prefix, Value)
                    VALUES (@RetailerId, 'AutoBalanced', 'CBTD', @BalanceSequentialNumber);
                END
                
                COMMIT TRANSACTION;
                
                -- Format the code with padded zeros
                SET @BalanceAdjustmentCode = 'CBTD' + RIGHT('000000' + CAST(@BalanceSequentialNumber AS NVARCHAR(10)), 6);
                
                -- Insert BalanceAdjustment for this partner delivery
                INSERT INTO BalanceAdjustment (
                    Code,
                    RetailerId,
                    AdjustedBy,
                    AdjustmentDate,
                    CreatedDate,
                    CreatedBy,
                    Description,
                    Balance,
                    PartnerType,
                    PartnerId
                )
                VALUES (
                    @BalanceAdjustmentCode,
                    @RetailerId,
                    @UserId,
                    @Now,
                    @Now,
                    @UserId,
                    @BalanceAdjustmentDescription,
                    -@PartnerDebt,
                    2,  -- PartnerType = 2 for PartnerDelivery
                    @PartnerId
                );
                
                SET @BalanceAdjustmentId = SCOPE_IDENTITY();
                SET @TotalRowsUpdated = @TotalRowsUpdated + 1;
                
                -- Record in auto-generated table for BalanceAdjustment
                INSERT INTO BalanceAdjustmentAutoGenerated (
                    RetailerId,
                    BranchId,
                    BalanceAdjustmentId,
                    RequestId,
                    Type,
                    CreatedDate
                )
                VALUES (
                    @RetailerId,
                    @BranchId,
                    @BalanceAdjustmentId,
                    @RequestId,
                    1, -- Type = 1 for balance adjustment
                    @Now
                );
                
                -- Insert into BalanceTracking for this partner delivery
                INSERT INTO BalanceTracking (
                    PartnerId,
                    DocumentId,
                    DocumentCode,
                    DocumentType,
                    Description,
                    Value,
                    Balance,
                    RetailerId,
                    TransDate,
                    DataZone
                )
                VALUES (
                    @PartnerId,
                    @BalanceAdjustmentId,
                    @BalanceAdjustmentCode,
                    1, -- DocumentType = 1 for balance adjustment
                    @BalanceAdjustmentDescription,
                    -@PartnerDebt, -- No value change 
                    -@PartnerDebt, -- Current debt balance
                    @RetailerId,
                    @Now,
                    2  -- DataZone = 2 for partner delivery
                );
                
                SET @TotalRowsUpdated = @TotalRowsUpdated + 1;
            END
            
            -- Commit the transaction for this partner delivery
            COMMIT TRANSACTION;
            
            -- Get next partner delivery
            FETCH NEXT FROM PartnerDeliveryCursor INTO @PartnerId, @PartnerDebt;
        END
        
        CLOSE PartnerDeliveryCursor;
        DEALLOCATE PartnerDeliveryCursor;
        
        -- Calculate total processed time in seconds
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        -- Close and deallocate cursor if open
        IF CURSOR_STATUS('local', 'PartnerDeliveryCursor') >= 0
        BEGIN
            CLOSE PartnerDeliveryCursor;
            DEALLOCATE PartnerDeliveryCursor;
        END
        
        -- Get error information
        SET @ErrorMsg = 
            'Error ' + CAST(ERROR_NUMBER() AS NVARCHAR(20)) + 
            ', Severity ' + CAST(ERROR_SEVERITY() AS NVARCHAR(20)) + 
            ', State ' + CAST(ERROR_STATE() AS NVARCHAR(20)) + 
            ', Line ' + CAST(ERROR_LINE() AS NVARCHAR(20)) + 
            ': ' + ERROR_MESSAGE();
            
        -- Set status to Failed
        SET @Status = 2;
        
        -- Calculate processed time up to error
        SET @ProcessedTime = DATEDIFF(SECOND, @StartTime, GETDATE());
    END CATCH
    
    -- Update DeleteDataDetail table regardless of success or failure
    UPDATE DeleteDataDetail
    SET 
        RecordCount = @TotalRowsUpdated,
        Status = @Status,
        ProcessedTime = @ProcessedTime,
        ProcessedDate = GETDATE(),
        ErrorMessage = @ErrorMsg
    WHERE 
        RequestId = @RequestId
        AND Type = 'AdjustmentPartnerDelivery'
        AND RetailerId = @RetailerId
        AND BranchId = @BranchId;
    
    -- Re-throw the original error if there was one
    IF @Status = 2
        RAISERROR(@ErrorMsg, 16, 1);
END
```

This procedure creates both a stock take record and a cost adjustment record that reflects the current inventory status and product costs after data deletion. This is useful for:

1. Providing a snapshot of inventory levels and costs at the time of data deletion
2. Allowing for manual verification of inventory accuracy and cost values
3. Serving as a reference point for future inventory and cost audits
4. Ensuring data consistency between product quantities, costs, and transaction history

The procedure should be called for each branch of the retailer after the transaction deletion is complete to document the final inventory state. The post-deletion process:

- Uses special code prefixes ('KKTD' for stock take and 'CBTD' for cost adjustment, balance, point) to distinguish them from regular operations
- Does not change any quantities or costs (just recording the current state)
- Includes all products that exist in ProductBranch for that branch
- Records costs for accurate inventory valuation
- Creates entries in InventoryTracking with appropriate document types (3 for stocktake, 13 for cost adjustment)
- Tracks the connection between these operations and the deletion request in BalanceAdjustmentAutoGenerated

### 4.4. Implementation Example

The following C# implementation shows how to integrate the inventory reconciliation into the deletion workflow:

```csharp
public async Task ReconcileInventoryAfterDeletion(int retailerId, int branchId, long requestId)
{
    // Create parameters for stored procedure
    var parameters = new[]
    {
        new SqlParameter("@RetailerId", SqlDbType.Int) { Value = retailerId },
        new SqlParameter("@BranchId", SqlDbType.Int) { Value = branchId },
        new SqlParameter("@RequestId", SqlDbType.BigInt) { Value = requestId },
        new SqlParameter("@UserId", SqlDbType.Int) { Value = 1 } // System user
    };

    // Execute the stored procedure
    var result = await _dbContext.Database
        .ExecuteSqlRawAsync("EXEC sp_GeneratePostDeletionInventory @RetailerId, @BranchId, @RequestId, @UserId", 
            parameters);

    _logger.LogInformation($"Generated post-deletion inventory records for retailer {retailerId}, branch {branchId}, request {requestId}");
}
```

## 5. Orchestration Service

A high-level orchestration service coordinates the execution of the deletion procedures:

```csharp
public class TransactionDeletionOrchestrator
{
    private readonly IDatabaseConnector _dbConnector;
    private readonly ILogger<TransactionDeletionOrchestrator> _logger;
    
    public async Task<OrchestrationResult> ExecuteDeleteRequestAsync(
        DeleteDataRequest request,
        CancellationToken cancellationToken)
    {
        // Validate request parameters
        
        // Determine deletion sequence based on dependencies
        var deletionSequence = DetermineDeletionSequence(request.FilterConditions);
        
        // Execute procedures in proper sequence
        foreach (var tableInfo in deletionSequence)
        {
            await ExecuteDeleteProcedureAsync(
                tableInfo.TableName,
                request.RetailerId,
                request.Id,
                tableInfo.BatchSize,
                tableInfo.TimeoutSeconds,
                request.FromDate,
                request.ToDate,
                cancellationToken);
            
            // Check for cancellation between operations
            if (cancellationToken.IsCancellationRequested)
                break;
        }
        
        // Generate balance adjustments if needed
        if (request.ShouldGenerateBalanceAdjustments)
        {
            await GenerateBalanceAdjustmentsAsync(
                request.RetailerId,
                request.Id,
                cancellationToken);
        }
        
        return new OrchestrationResult
        {
            Success = true,
            // Additional result properties
        };
    }
    
    private async Task ExecuteDeleteProcedureAsync(
        string tableName,
        int retailerId,
        long requestId,
        int batchSize,
        int timeoutSeconds,
        DateTime? fromDate,
        DateTime? toDate,
        CancellationToken cancellationToken)
    {
        var procedureName = $"pr_DataManagement_Delete{tableName}Data";
        
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("@RetailerId", retailerId),
            new SqlParameter("@RequestId", requestId),
            new SqlParameter("@BatchSize", batchSize),
            new SqlParameter("@MaxExecutionSeconds", timeoutSeconds),
            new SqlParameter("@FromDate", fromDate ?? (object)DBNull.Value),
            new SqlParameter("@ToDate", toDate ?? (object)DBNull.Value)
        };
        
        await _dbConnector.ExecuteStoredProcedureAsync(
            procedureName,
            parameters,
            cancellationToken);
    }
}
```

## 6. Restoration Process

The restoration process reverts all RetailerId changes:

```sql
CREATE OR ALTER PROCEDURE [dbo].[sp_RestoreRetailerTransactionData]
    @RetailerId INT,
    @RequestId BIGINT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @NegativeRetailerId INT;
    SET @NegativeRetailerId = -CAST(CAST(ABS(@RetailerId) AS BIGINT) * 10000 + (@RequestId % 10000) AS INT);
    
    -- Restore each table in reverse deletion order
    DECLARE @TableNames TABLE (Type NVARCHAR(100), ProcessOrder INT);
    INSERT INTO @TableNames
    VALUES
        ('BalanceAdjustment', 1),
        ('BalanceTracking', 2),
        ('InventoryTracking', 3),
        ('StockTake', 4),
        ('OrderSummaryInfo', 5),
        ('Order', 6),
        ('DamageItem', 7),
        ('Manufacturing', 8),
        ('CashFlow', 9),
        ('Payment', 10),
        ('Transfer', 11),
        ('Return', 12),
        ('PurchaseReturn', 13),
        ('PurchaseOrder', 14),
        ('Invoice', 15),
    
    DECLARE @CurrentTable NVARCHAR(100);
    DECLARE @SQL NVARCHAR(MAX);
    
    DECLARE TableCursor CURSOR FOR
    SELECT Type FROM @TableNames ORDER BY ProcessOrder;
    
    OPEN TableCursor;
    FETCH NEXT FROM TableCursor INTO @CurrentTable;
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        SET @SQL = N'
        UPDATE [' + @CurrentTable + '] WITH (ROWLOCK)
        SET RetailerId = ' + CAST(@RetailerId AS NVARCHAR(20)) + '
        WHERE RetailerId = ' + CAST(@NegativeRetailerId AS NVARCHAR(20)) + ';';
        
        EXEC sp_executesql @SQL;
        
        -- Update status in DeleteDataDetail
        UPDATE DeleteDataDetail
        SET Status = 3 -- Restored
        WHERE RequestId = @RequestId
        AND Type = @CurrentTable
        AND RetailerId = @RetailerId;
        
        FETCH NEXT FROM TableCursor INTO @CurrentTable;
    END
    
    CLOSE TableCursor;
    DEALLOCATE TableCursor;
END
```

## 7. Performance Optimization

### 7.1. Recommended Indexes

```sql
-- Index examples for each transaction table
CREATE INDEX IX_Invoice_RetailerId_DeleteData ON Invoice(RetailerId, PurchaseDate) 
WHERE RetailerId > 0;

CREATE INDEX IX_InvoiceDetail_RetailerId_DeleteData ON InvoiceDetail(RetailerId, InvoiceId) 
WHERE RetailerId > 0;

CREATE INDEX IX_Payment_RetailerId_DeleteData ON Payment(RetailerId, TransactionDate) 
WHERE RetailerId > 0;

-- Similar indexes for other transaction tables
```

### 7.2. Table-Specific Batch Sizes

Recommended batch sizes for different tables:

| Table Name | Recommended Batch Size | Timeout (seconds) |
|------------|------------------------|-------------------|
| Invoice    | 5,000                  | 600               |
| Payment    | 5,000                  | 600               |
| PurchaseOrder | 3,000               | 600               |
| Transfer   | 2,000                  | 600               |
| CashFlow   | 10,000                 | 600               |

## 8. Error Handling Strategy

- Automatic rollback of transactions on errors
- Detailed error logging in DeleteDataDetail table
- Retry mechanism with exponential backoff for transient errors
- Status reporting for partial completion scenarios

## 9. Monitoring and Notifications

- Real-time monitoring dashboard for deletion progress
- Email notifications on completion/failure
- Audit trail for all deletion operations
- Performance metrics and execution statistics

## 10. Integration Testing Scenarios

Key test scenarios to verify transaction table deletion:

1. Delete transaction data with active e-invoices (should fail)
2. Delete transaction data with closed accounting periods (should fail)
3. Delete transaction data with date filters (should succeed)
4. Delete and restore transaction data (should succeed and verify data integrity)
5. Performance testing with large datasets
6. Concurrent deletion requests (should handle locking appropriately)
7. System failure during deletion (recovery process)
8. Balance adjustment verification after deletion 



