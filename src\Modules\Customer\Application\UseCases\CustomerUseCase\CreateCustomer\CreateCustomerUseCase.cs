using KvFnB.Core.Abstractions;
using KvFnB.Core.Authentication;
using KvFnB.Core.Constants;
using KvFnB.Core.Contracts;
using KvFnB.Core.Domain.Services;
using KvFnB.Core.Enums;
using KvFnB.Core.Validation;
using KvFnB.Modules.Customer.Domain.Repositories;
using KvFnB.Shared.AuditTrailMessagePublisher;
using System.Text.Json;
using KvFnB.Modules.Customer.Domain.Commands;
using KvFnB.Modules.Customer.Application.Dtos;
using KvFnB.Shared.MultiTenancy;
using KvFnB.Core.Domain.Models;

namespace KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.CreateCustomer
{
    /// <summary>
    /// Implements the CreateCustomer use case
    /// </summary>
    public class CreateCustomerUseCase : UseCaseBase<CreateCustomerRequest, CreateCustomerResponse>
    {
        private readonly IValidator<CreateCustomerRequest> _validator;
        private readonly ICustomerRepository _customerRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly AutoMapper.IMapper _mapper;
        private readonly ITenantProvider _tenantProvider;
        private readonly IAuthUser _authUser;
        private readonly ILogger _logger;
        private readonly ICodeGenerationFromTbService _codeGenerationService;
        private readonly IAuditTrailService _auditTrailService;
        private readonly ICommandDispatcher _commandDispatcher;
        private readonly ICustomerGroupRepository _customerGroupRepository;
        private readonly TenantConfiguration _tenantConfiguration;
        private readonly IAddressDomainService _addressDomainService;
        private const string CUSTOMER_CODE_PREFIX = "KH";

        /// <summary>
        /// Initializes a new instance of the <see cref="CreateCustomerUseCase"/> class.
        /// </summary>
        /// <param name="validator">The validator for request validation.</param>
        /// <param name="customerRepository">The repository for customer data operations.</param>
        /// <param name="unitOfWork">The unit of work for transaction management.</param>
        /// <param name="mapper">The mapper for object mapping.</param>
        /// <param name="tenantProvider">The tenant provider for multi-tenancy support.</param>
        /// <param name="authUser">The authenticated user information.</param>
        /// <param name="logger">The logger for logging operations.</param>
        /// <param name="codeGenerationService">The service for generating unique codes.</param>
        /// <param name="auditTrailService">The service for audit trail logging.</param>
        /// <param name="commandDispatcher">The dispatcher for sending commands to Kafka.</param>
        /// <param name="tenantConfiguration">The tenant configuration.</param>
        /// <param name="customerGroupRepository">The repository for customer group data operations.</param>
        /// <param name="addressDomainService">The service for address domain operations.</param>
        public CreateCustomerUseCase(
            IValidator<CreateCustomerRequest> validator,
            ICustomerRepository customerRepository,
            IUnitOfWork unitOfWork,
            AutoMapper.IMapper mapper,
            ITenantProvider tenantProvider,
            IAuthUser authUser,
            ILogger logger,
            ICodeGenerationFromTbService codeGenerationService,
            IAuditTrailService auditTrailService,
            ICommandDispatcher commandDispatcher,
            TenantConfiguration tenantConfiguration,
            ICustomerGroupRepository customerGroupRepository,
            IAddressDomainService addressDomainService
            )
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _customerRepository = customerRepository ?? throw new ArgumentNullException(nameof(customerRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _authUser = authUser ?? throw new ArgumentNullException(nameof(authUser));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _codeGenerationService = codeGenerationService ?? throw new ArgumentNullException(nameof(codeGenerationService));
            _auditTrailService = auditTrailService ?? throw new ArgumentNullException(nameof(auditTrailService));
            _commandDispatcher = commandDispatcher ?? throw new ArgumentNullException(nameof(commandDispatcher));
            _customerGroupRepository = customerGroupRepository ?? throw new ArgumentNullException(nameof(customerGroupRepository));
            _tenantConfiguration = tenantConfiguration ?? throw new ArgumentNullException(nameof(tenantConfiguration));
            _addressDomainService = addressDomainService ?? throw new ArgumentNullException(nameof(addressDomainService));
        }

        /// <summary>
        /// Executes the CreateCustomer use case.
        /// </summary>
        /// <param name="request">The request containing customer data.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A result containing the created customer or error information.</returns>
        public override async Task<Result<CreateCustomerResponse>> ExecuteAsync(
            CreateCustomerRequest request, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                StrimRequest(request);
                var validationResult = _validator.Validate(request);
                if (!validationResult.IsValid)
                {
                    return Result<CreateCustomerResponse>.Failure(validationResult.Errors);
                }

                // Check if customer code already exists
                if (!string.IsNullOrEmpty(request.Code))
                {
                    var isCodeUnique = await _customerRepository.IsCodeUniqueAsync(request.Code, cancellationToken);
                    if (!isCodeUnique)
                    {
                        return Result<CreateCustomerResponse>.Failure($"Customer with code '{request.Code}' already exists.");
                    }
                }

                // Check if customer contact number already exists
                if(!_tenantConfiguration.AllowDuplicateCustomerPhone)
                {
                    var hasCustomer = await _customerRepository.HasCustomerByContactNumberAsync(request.ContactNumber ?? "", cancellationToken);
                    if(hasCustomer)
                    {
                        return Result<CreateCustomerResponse>.Failure($"Customer with contact number already exists.");
                    }
                }
                // Map request to customer entity
                var customer = _mapper.Map<Domain.Models.Customer>(request);
                
                // Set tenant ID for multi-tenancy
                customer.TenantId = _tenantProvider.GetTenantId() ?? throw new InvalidOperationException("Tenant ID is required");

                // Generate code if not provided
                if (string.IsNullOrEmpty(request.Code))
                {
                    customer.Code = await GenerateCustomerCodeAsync();
                }

                var address = new Address
                {
                    AddressLine = request.AddressLocation?.AddressLine,
                    AdministrativeAreaId = request.AddressLocation?.AdministrativeAreaId ?? 0,
                    PostalCode = request.AddressLocation?.PostalCode
                };

                // Save address location
                await _addressDomainService.SaveAddressLocation(address, cancellationToken);
                // Add the customer to the repository
                if(address.Id > 0) 
                {
                    customer.UpdateAddress(address.Id, address.AddressLine ?? string.Empty);
                }

                // Add customer group details
                if (request.GroupIds != null && request.GroupIds.Any())
                {
                    customer.UpdateCustomerGroupDetails();
                }

                customer = await _customerRepository.AddAsync(customer, cancellationToken);
                // Commit changes using unit of work
                await _unitOfWork.CommitAsync(cancellationToken);

                // Map entity to response
                var response = _mapper.Map<CreateCustomerResponse>(customer);

                // Load customer groups if requested
                if (request.GroupIds != null && request.GroupIds.Any())
                {
                    var groups = await _customerGroupRepository.GetByIdsAsync(request.GroupIds, cancellationToken);
                    response.CustomerGroups = _mapper.Map<List<CustomerGroupResponse>>(groups);
                }

                // Create audit log
                await CreateAuditLogAsync(customer);
                
                // Send digital marketing event
                await SendDigitalMarketingEventAsync(response, cancellationToken);

                return Result<CreateCustomerResponse>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.Error("Error occurred while creating customer", ex);
                return Result<CreateCustomerResponse>.Failure(ErrorMessages.InternalServerError);
            }
        }

        private async Task<string> GenerateCustomerCodeAsync()
        {
            return await _codeGenerationService.GenerateNextCodeAsync(
                typeof(Domain.Models.Customer),
                CUSTOMER_CODE_PREFIX,
                6
            );
        }


        /// <summary>
        /// Creates an audit log entry for customer creation
        /// </summary>
        /// <param name="customer">The created customer</param>
        /// <returns>Task representing the async operation</returns>
        private async Task CreateAuditLogAsync(Domain.Models.Customer customer)
        {
            try
            {
                var tenantId = _tenantProvider.GetTenantId() ?? 0;
                var branchId = _tenantProvider.GetBranchId() ?? 0;
                
                // Create serializable audit content with sensitive data removed/masked
                var auditContent = new
                {
                    customer.Id,
                    customer.Code,
                    customer.Name,
                    customer.Type,
                    EmailProvided = !string.IsNullOrEmpty(customer.Email),
                    PhoneProvided = !string.IsNullOrEmpty(customer.ContactNumber),
                    customer.IsActive,
                    customer.CreatedAt
                };
                
                // Create the audit log
                var auditLog = new AuditTrailLog
                {
                    FunctionId = (int)FunctionType.Customer,
                    Action = (int)AuditAction.Create,
                    Content = JsonSerializer.Serialize(auditContent),
                    UserName = _authUser.UserName,
                    LotId = Guid.NewGuid().ToString("N"),
                    KeyWords = $"{customer.Code}|{customer.Name}",
                    RefId = (int)customer.Id,
                    RetailerId = tenantId,
                    BranchId = (int)branchId,
                    UserId = _authUser.Id,
                    CreatedDate = DateTime.Now
                };
                
                await _auditTrailService.AddLog(auditLog);
            }
            catch (Exception ex)
            {
                // Just log the error but don't rethrow - audit logging should not prevent main functionality
                _logger.Error($"Failed to create audit log for customer creation. ID: {customer.Id}", ex);
            }
        }
        
        /// <summary>
        /// Sends a digital marketing event for customer registration
        /// </summary>
        /// <param name="customer">The created customer</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        private async Task SendDigitalMarketingEventAsync(CreateCustomerResponse customer, CancellationToken cancellationToken)
        {
            try
            {
                if (
                    customer.Id <= 0 || 
                    string.IsNullOrEmpty(customer.ContactNumber) || 
                    string.IsNullOrEmpty(customer.Code))
                {
                    return;
                }
                
                // Create groups string from customer groups
                var groups = string.Join("|", customer.CustomerGroups?.Select(g => g.Name) ?? Array.Empty<string>());
                if (string.IsNullOrEmpty(groups))
                {
                    groups = "Đã là thành viên";
                }
                
                // Create the digital registration event
                var registerMember = new DigitalRegisterMemberSuccessedEvent(
                    commandIssuer: "KvFnB-API",
                    retailerCode: _tenantProvider.GetRetailerCode() ?? string.Empty,
                    retailerId: _tenantProvider.GetTenantId() ?? 0,
                    branchId: _tenantProvider.GetBranchId() ?? 0,
                    groupId: _tenantProvider.GetShardId() ?? 0,
                    userId: _authUser.Id,
                    customerId: customer.Id,
                    contactNumber: customer.ContactNumber,
                    customerName: customer.Name,
                    rewardPoint: customer.RewardPoint ?? 0,
                    customerCode: customer.Code,
                    groupName: groups
                );
                
                // Send the event
                await _commandDispatcher.SendAsync(registerMember, "TopicDigitalMarketing");
            }
            catch (Exception ex)
            {
                // Log error but don't fail the operation
                _logger.Error(string.Format("Error sending digital registration event for customer {0}", customer.Id), ex);
            }
        }

        /// <summary>
        /// Trims the request data
        /// </summary>
        /// <param name="request">The request to trim</param>
        private static void StrimRequest(CreateCustomerRequest request)
        {
            request.Code = request.Code?.Trim() ?? string.Empty;
            request.Email = request.Email?.Trim() ?? string.Empty;
            request.ContactNumber = request.ContactNumber?.Trim() ?? string.Empty;
            request.Name = request.Name?.Trim() ?? string.Empty;
            request.Address = request.Address?.Trim() ?? string.Empty;
            request.Comments = request.Comments?.Trim() ?? string.Empty;
            request.Organization = request.Organization?.Trim() ?? string.Empty;
            request.TaxCode = request.TaxCode?.Trim() ?? string.Empty;
        }
    }
} 