using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.CreateProductCategory;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.UpdateProductCategory;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.RemoveProductCategory;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.GetProductCategoryDetail;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.GetProductCategories;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using KvFnB.Modules.Menu.Application.UseCases.CategoryUseCase.RankingProductCategory;
using KvFnB.Core.Abstractions;
using KvFnB.Shared.Filters;
using Microsoft.AspNetCore.Http;

namespace KvFnB.Modules.Menu.Restful.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CategoryController : BaseController
    {
        private readonly CreateProductCategoryUseCase _createUseCase;
        private readonly UpdateProductCategoryUseCase _updateUseCase;
        private readonly RemoveProductCategoryUseCase _deleteUseCase;
        private readonly GetProductCategoryDetailUseCase _getCategoryDetailUseCase;
        private readonly GetProductCategoriesUseCase _getCategoriesUseCase;
        private readonly RankCategoryUseCase _rankCategoryUseCase;

        public CategoryController(
            CreateProductCategoryUseCase createUseCase,
            UpdateProductCategoryUseCase updateUseCase,
            RemoveProductCategoryUseCase deleteUseCase,
            GetProductCategoryDetailUseCase getCategoryDetailUseCase,
            GetProductCategoriesUseCase getCategoriesUseCase,
            RankCategoryUseCase rankCategoryUseCase,
            IHttpContextAccessor httpContextAccessor
            ) : base(httpContextAccessor)
        {
            _createUseCase = createUseCase ?? throw new ArgumentNullException(nameof(createUseCase));
            _updateUseCase = updateUseCase ?? throw new ArgumentNullException(nameof(updateUseCase));
            _deleteUseCase = deleteUseCase ?? throw new ArgumentNullException(nameof(deleteUseCase));
            _getCategoryDetailUseCase = getCategoryDetailUseCase ?? throw new ArgumentNullException(nameof(getCategoryDetailUseCase));
            _getCategoriesUseCase = getCategoriesUseCase ?? throw new ArgumentNullException(nameof(getCategoriesUseCase));
            _rankCategoryUseCase = rankCategoryUseCase ?? throw new ArgumentNullException(nameof(rankCategoryUseCase));
        }

        /// <summary>
        /// Creates a new product category.
        /// </summary>
        /// <param name="request">The request object containing the details of the category to create.</param>
        /// <returns>A response indicating the result of the create operation.</returns>
        /// <response code="200">Returns the newly created category.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPost]
        [SwaggerOperation(Summary = "Creates a new product category", Description = "Creates a new product category with the provided details.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the newly created category", typeof(CreateProductCategoryResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> CreateCategory([FromBody] CreateProductCategoryRequest request)
        {
            var result = await _createUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Updates an existing product category.
        /// </summary>
        /// <param name="id">The ID of the category to update.</param>
        /// <param name="request">The request object containing the updated details of the category.</param>
        /// <returns>A response indicating the result of the update operation.</returns>
        /// <response code="200">Returns the updated category.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPut("{id}")]
        [SwaggerOperation(Summary = "Updates an existing product category", Description = "Updates an existing product category with the provided details.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the updated category", typeof(UpdateProductCategoryResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> UpdateCategory(int id, [FromBody] UpdateProductCategoryRequest request)
        {
            request.Id = id;
            var result = await _updateUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Deletes an existing product category.
        /// </summary>
        /// <param name="id">The ID of the category to delete.</param>
        /// <returns>A response indicating the result of the delete operation.</returns>
        /// <response code="200">Returns the deleted category.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpDelete("{id}")]
        [SwaggerOperation(Summary = "Deletes an existing product category", Description = "Deletes an existing product category by ID.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the deleted category", typeof(RemoveProductCategoryResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> DeleteCategory(int id)
        {
            var request = new RemoveProductCategoryRequest { Id = id };
            var result = await _deleteUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Gets the details of a product category.
        /// </summary>
        /// <param name="id">The ID of the category to get details for.</param>
        /// <returns>A response indicating the result of the get operation.</returns>
        /// <response code="200">Returns the category details.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpGet("{id}")]
        [SwaggerOperation(Summary = "Gets the details of a product category", Description = "Gets the details of a product category by ID.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the category details", typeof(GetProductCategoryDetailResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> GetCategoryDetail(int id)
        {
            var request = new GetProductCategoryDetailRequest { Id = id };
            var result = await _getCategoryDetailUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

        /// <summary>
        /// Gets a list of product categories.
        /// </summary>
        /// <returns>A response indicating the result of the get operation.</returns>
        /// <response code="200">Returns the list of categories.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpGet]
        [SwaggerOperation(Summary = "Gets a list of product categories", Description = "Gets a list of product categories.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the list of categories", typeof(IEnumerable<GetProductCategoriesResponse>))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid")]
        public async Task<IActionResult> GetCategories(GetProductCategoriesRequest request)
        {
            var result = await _getCategoriesUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }

       
         /// <summary>
        /// Ranks a product category.
        /// </summary>
        /// <param name="request">The request object containing the category ID, pre-category ID, and next-category ID.</param>
        /// <returns>A response indicating the result of the ranking operation.</returns>
        /// <response code="200">Returns the ranking result.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPost("rank")]
        [SwaggerOperation(Summary = "Ranks a product category", Description = "Ranks a product category based on the provided pre and next category IDs.")]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the ranking result", typeof(SuccessResponse<RankCategoryResponse>))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid", typeof(ErrorResponse))]
         public async Task<IActionResult> RankCategory([FromBody] RankCategoryRequest request)
        {
            var result = await _rankCategoryUseCase.ExecuteAsync(request);
            if (!result.IsSuccess)
            {
                return Failure(result);
            }
            return Ok(result.Value);
        }
    }
}