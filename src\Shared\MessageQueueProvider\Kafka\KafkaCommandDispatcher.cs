using System;
using System.Text.Json;
using System.Threading.Tasks;
using Confluent.Kafka;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Domain.Commands;
using KvFnB.Shared.MessageQueueProvider.Kafka.Models;

namespace KvFnB.Shared.MessageQueueProvider.Kafka
{
    /// <summary>
    /// Implements the ICommandDispatcher interface using Apache Kafka.
    /// </summary>
    public class KafkaCommandDispatcher : ICommandDispatcher, IDisposable
    {
        private readonly KafkaConfig _kafkaConfig;
        private readonly ILogger _logger;
        private IProducer<string, string> _producer;
        private readonly object _lockObj = new object();
        private bool _disposed;

        public KafkaCommandDispatcher(KafkaConfig kafkaConfig, ILogger logger)
        {
            _kafkaConfig = kafkaConfig ?? throw new ArgumentNullException(nameof(kafkaConfig));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Sends a digital command to the specified Kafka topic.
        /// </summary>
        /// <typeparam name="TCommand">Type of the command</typeparam>
        /// <param name="command">The command to send</param>
        /// <param name="topic">Optional topic name. If not provided, the default topic will be used.</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task SendAsync<TCommand>(TCommand command, string topic = null) where TCommand : BaseDigitalCommand
        {
            if (command == null)
                throw new ArgumentNullException(nameof(command));

            if (string.IsNullOrWhiteSpace(topic))
            {
                if (string.IsNullOrWhiteSpace(_kafkaConfig.DefaultTopic))
                    throw new InvalidOperationException("No topic specified and no default topic configured.");
                
                topic = _kafkaConfig.DefaultTopic;
            }

            EnsureProducerInitialized();

            try
            {
                string serializedCommand = JsonSerializer.Serialize(command);
                var commandDto = new DigitalCommandDto(command, serializedCommand);
                string serializedDto = JsonSerializer.Serialize(commandDto);

                var message = new Message<string, string>
                {
                    Key = command.EventId.ToString(),
                    Value = serializedDto
                };

                var deliveryResult = await _producer.ProduceAsync(topic, message);
                
                if (deliveryResult.Status == PersistenceStatus.NotPersisted)
                {
                    _logger.Error($"Failed to deliver message to topic {topic}. Event ID: {command.EventId}");
                    throw new Exception($"Failed to deliver message to topic {topic}. Event ID: {command.EventId}");
                }

            }
            catch (ProduceException<string, string> ex)
            {
                _logger.Error($"Error producing Kafka message. Event ID: {command.EventId}", ex);
                throw;
            }
            catch (Exception ex)
            {
                _logger.Error($"Unexpected error in Kafka command dispatcher. Event ID: {command.EventId}", ex);
                throw;
            }
        }

        /// <summary>
        /// Ensures that the Kafka producer is initialized.
        /// </summary>
        private void EnsureProducerInitialized()
        {
            if (_producer != null) return;

            lock (_lockObj)
            {
                if (_producer != null) return;

                var producerConfig = new ProducerConfig
                {
                    BootstrapServers = _kafkaConfig.BootstrapServers,
                    MessageMaxBytes = _kafkaConfig.MessageMaxBytes,
                    ClientId = _kafkaConfig.ClientId
                };

                var producerBuilder = new ProducerBuilder<string, string>(producerConfig)
                    .SetErrorHandler((_, e) =>
                    {
                        _logger.Error($"Kafka producer error: {e.Reason}");
                    });

                _producer = producerBuilder.Build();
            }
        }

        /// <summary>
        /// Disposes the Kafka producer.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Releases unmanaged and optionally managed resources.
        /// </summary>
        /// <param name="disposing">True to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return;

            if (disposing)
            {
                _producer?.Dispose();
            }

            _disposed = true;
        }
    }
} 