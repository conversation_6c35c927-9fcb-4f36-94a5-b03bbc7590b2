using KvFnB.Core.Abstractions;
using KvFnB.Shared.Persistence.ShardingDb;
using KvFnB.Shared.Persistence.ShardingDb.Services;
using Microsoft.Extensions.Logging;
using Moq;
using KvFnB.Core.Domain.Services;
using KvFnB.Core.Authentication;
using Microsoft.EntityFrameworkCore;

namespace KvFnB.Shared.Persistence.Tests
{
    public class CodeGenerationServiceTests
    {
        private readonly Mock<ITenantProvider> _mockTenantProvider;
        private readonly Mock<ILogger<CodeGenerationService>> _mockLogger;
        private readonly Mock<IAuthUser> _mockAuthUser;
        private readonly ICodeGenerationFromTbService _service;

        public CodeGenerationServiceTests()
        {
            _mockTenantProvider = new Mock<ITenantProvider>();
            _mockLogger = new Mock<ILogger<CodeGenerationService>>();
            _mockAuthUser = new Mock<IAuthUser>();
            
            // Instead of mocking ShardingDbContext directly, we'll create a test version that doesn't need a real DB
            var dbOptions = new DbContextOptionsBuilder<ShardingDbContext>()
                .UseInMemoryDatabase("CodeGenerationServiceTests")
                .Options;
                
            var dbContext = new ShardingDbContext(_mockTenantProvider.Object, _mockAuthUser.Object, dbOptions);
            
            _service = new CodeGenerationService(
                dbContext,
                _mockLogger.Object,
                _mockTenantProvider.Object);
        }

        [Theory]
        [InlineData("INV-", null, 5, "INV-00001")]
        [InlineData("INV-", "INV-00001", 5, "INV-00002")]
        [InlineData("INV-", "INV-00009", 5, "INV-00010")]
        [InlineData("INV-", "INV-00099", 5, "INV-00100")]
        [InlineData("INV-", "INV-99999", 5, "INV-100000")]
        [InlineData("ORD/", "ORD/123", 3, "ORD/124")]
        [InlineData("CUST-", "CUST-9", 1, "CUST-10")]
        public void GenerateNextCode_ShouldIncrementPreviousCode(
            string prefix, string previousCode, int padding, string expected)
        {
            // Act
            var result = _service.GenerateNextCode(prefix, previousCode, padding);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void GenerateNextCode_WithNoPrefix_ShouldThrowArgumentException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => 
                _service.GenerateNextCode(string.Empty, "123", 5));
            
            Assert.Equal("Prefix cannot be null or empty (Parameter 'prefix')", exception.Message);
        }

        [Fact]
        public void GenerateNextCode_WithInvalidPadding_ShouldThrowArgumentException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => 
                _service.GenerateNextCode("INV-", "123", 0));
            
            Assert.Equal("Padding must be at least 1 (Parameter 'padding')", exception.Message);
        }

        [Fact]
        public void GenerateNextCode_WithInvalidPreviousCode_ShouldReturnFirstCode()
        {
            // Arrange
            string prefix = "INV-";
            string previousCode = "INV-ABCDEF"; // No numeric part
            int padding = 5;
            string expected = "INV-00001";

            // Act
            var result = _service.GenerateNextCode(prefix, previousCode, padding);

            // Assert
            Assert.Equal(expected, result);
            
            // Verify warning was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((o, t) => o.ToString().Contains("Could not extract numeric portion from code")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public void GenerateNextCode_WithLargePreviousNumber_ShouldHandleOverflow()
        {
            // Arrange
            string prefix = "INV-";
            string previousCode = "INV-9999999999"; // Near long.MaxValue
            int padding = 10;

            // Act
            var result = _service.GenerateNextCode(prefix, previousCode, padding);

            // Assert
            Assert.Equal("INV-10000000000", result);
        }
    }
} 