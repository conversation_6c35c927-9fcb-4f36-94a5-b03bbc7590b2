using KvFnB.Core.Abstractions;
using StackExchange.Redis;
using System;

namespace KvFnB.Shared.StackExchangeRedis
{
    /// <summary>
    /// An adapter implementation that wraps a ConnectionMultiplexer for use with Redis message queues.
    /// This provides a simplified interface while allowing access to the full IConnectionMultiplexer.
    /// </summary>
    public class RedisMqConnectionAdapter : IRedisMqConnection
    {
        private readonly ConnectionMultiplexer _connection;
        
        public RedisMqConnectionAdapter(IRedisConfiguration configuration, ILogger logger)
        {
            ArgumentNullException.ThrowIfNull(configuration);
            ArgumentNullException.ThrowIfNull(logger);
            
            _connection = RedisManager.GetRedisConnection(configuration, logger);
        }
        
        /// <inheritdoc/>
        public bool IsConnected => _connection.IsConnected;
        
        /// <inheritdoc/>
        public IDatabase GetDatabase(int db = -1, object asyncState = null) => _connection.GetDatabase(db, asyncState);
        
        /// <inheritdoc/>
        public ISubscriber GetSubscriber(object asyncState = null) => _connection.GetSubscriber(asyncState);
        
        /// <inheritdoc/>
        public void Dispose() => _connection.Dispose();
        
        /// <inheritdoc/>
        public IConnectionMultiplexer AsIConnectionMultiplexer() => _connection;
    }
} 